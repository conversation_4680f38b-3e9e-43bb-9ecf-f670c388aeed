import apiClient from "@/lib/apiClient";
import { ExpenseBreakdown, ExpenseRecord, MonthlyExpense } from "@/services/funding-service";

// Define interfaces for API responses
export interface ExpenseHistoryResponse {
  status: string;
  data: {
    total_budget: number;
    total_spent: number;
    remaining_budget: number;
    category_breakdown: ExpenseBreakdown[];
    monthly_expenses: MonthlyExpense[];
    expense_records: Array<{
      id: number;
      grant: number;
      particulars: string;
      main_headers: string;
      sub_headers: string;
      budget_q1: number;
      budget_q2: number;
      budget_q3: number;
      budget_q4: number;
      actual_q1: number;
      actual_q2: number;
      actual_q3: number;
      actual_q4: number;
      total_budget: number;
      total_actual: number;
      receipt: string | null;
      expense_date: string;
    }>;
  };
}

export interface ExpenseHistoryData {
  totalBudget: number;
  totalSpent: number;
  remainingBudget: number;
  categoryBreakdown: ExpenseBreakdown[];
  monthlyExpenses: MonthlyExpense[];
  expenseRecords: ExpenseRecord[];
}

/**
 * Fetches expense history data from the backend
 * @param year The year to filter expenses by
 * @param quarter The quarter to filter expenses by (optional)
 * @param category The category to filter expenses by (optional)
 * @returns Promise with expense history data
 */
export const getExpenseHistory = async (
  year: string,
  quarter: string = 'All',
  category: string = 'All'
): Promise<ExpenseHistoryData> => {
  try {
    // Build query parameters
    const params: Record<string, string> = { year };
    if (quarter !== 'All') params.quarter = quarter;
    if (category !== 'All') params.category = category;

    // Get the backend URL from environment variables
    const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URI || 'http://localhost:8000';

    // Make API request
    const response = await apiClient.get<ExpenseHistoryResponse>(`${API_BASE_URL}/api/funding/v1/expenses/`, { params });

    // Transform API response to match our interface
    const data = response.data.data;

    // Map backend expense records to frontend format
    const expenseRecords: ExpenseRecord[] = data.expense_records.map(record => ({
      id: record.id.toString(),
      loggedDate: record.expense_date,
      totalBudget: record.total_budget,
      totalActualSpent: record.total_actual,
      status: 'Verified', // Default status
      attachment: record.receipt || 'Manual Entry',
      category: record.main_headers,
      description: record.particulars,
      source_type: record.receipt ? 'excel' : 'manual',
      budget_q1: record.budget_q1,
      budget_q2: record.budget_q2,
      budget_q3: record.budget_q3,
      budget_q4: record.budget_q4,
      actual_q1: record.actual_q1,
      actual_q2: record.actual_q2,
      actual_q3: record.actual_q3,
      actual_q4: record.actual_q4,
      receipt: record.receipt
    }));

    return {
      totalBudget: data.total_budget,
      totalSpent: data.total_spent,
      remainingBudget: data.remaining_budget,
      categoryBreakdown: data.category_breakdown,
      monthlyExpenses: data.monthly_expenses,
      expenseRecords
    };
  } catch (error) {
    console.error('Error fetching expense history:', error);
    // For development, return mock data if API call fails
    return {
      totalBudget: 1100000,
      totalSpent: 850000,
      remainingBudget: 250000,
      categoryBreakdown: [
        { name: 'Personnel', value: 350000, percentage: '41.2%', color: '#FF9800' },
        { name: 'Operations', value: 250000, percentage: '29.4%', color: '#FFC107' },
        { name: 'Programs', value: 150000, percentage: '17.6%', color: '#FFD54F' },
        { name: 'Equipment', value: 100000, percentage: '11.8%', color: '#FFECB3' },
      ],
      monthlyExpenses: [
        { month: 'Jan', budget: 90000, actual: 85000 },
        { month: 'Feb', budget: 90000, actual: 88000 },
        { month: 'Mar', budget: 90000, actual: 92000 },
        { month: 'Apr', budget: 90000, actual: 87000 },
        { month: 'May', budget: 90000, actual: 91000 },
        { month: 'Jun', budget: 90000, actual: 89000 },
        { month: 'Jul', budget: 90000, actual: 94000 },
        { month: 'Aug', budget: 90000, actual: 90000 },
        { month: 'Sep', budget: 90000, actual: 85000 },
        { month: 'Oct', budget: 90000, actual: 85000 },
        { month: 'Nov', budget: 90000, actual: 82000 },
        { month: 'Dec', budget: 90000, actual: 80000 },
      ],
      expenseRecords: [
        {
          id: '1',
          loggedDate: '2024-01-15',
          totalBudget: 120000,
          totalActualSpent: 115000,
          status: 'Verified',
          attachment: 'expense-report-jan.xlsx',
          category: 'Personnel',
          description: 'Staff salaries and benefits',
          source_type: 'excel',
        },
        {
          id: '2',
          loggedDate: '2024-02-10',
          totalBudget: 80000,
          totalActualSpent: 78000,
          status: 'Verified',
          attachment: 'expense-report-feb.xlsx',
          category: 'Operations',
          description: 'Office rent and utilities',
          source_type: 'excel',
        },
        {
          id: '3',
          loggedDate: '2024-03-05',
          totalBudget: 50000,
          totalActualSpent: 52000,
          status: 'Verified',
          attachment: 'expense-report-mar.xlsx',
          category: 'Programs',
          description: 'Community outreach programs',
          source_type: 'excel',
        },
        {
          id: '4',
          loggedDate: '2024-04-20',
          totalBudget: 40000,
          totalActualSpent: 38000,
          status: 'Verified',
          attachment: 'expense-report-apr.xlsx',
          category: 'Equipment',
          description: 'Computer equipment and software',
          source_type: 'excel',
        },
        {
          id: '5',
          loggedDate: '2024-05-12',
          totalBudget: 90000,
          totalActualSpent: 91000,
          status: 'Verified',
          attachment: 'expense-report-may.xlsx',
          category: 'Personnel',
          description: 'Staff training and development',
          source_type: 'excel',
        },
      ],
    };
  }
};

/**
 * Fetches a single expense record by ID
 * @param id The expense record ID
 * @returns Promise with expense record data
 */
export const getExpenseById = async (id: string): Promise<ExpenseRecord> => {
  try {
    // Get the backend URL from environment variables
    const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URI || 'http://localhost:8000';

    const response = await apiClient.get<{ status: string; data: ExpenseHistoryResponse['data']['expense_records'][0] }>(
      `${API_BASE_URL}/api/funding/v1/expenses/${id}/`
    );

    const record = response.data.data;

    return {
      id: record.id.toString(),
      loggedDate: record.expense_date,
      totalBudget: record.total_budget,
      totalActualSpent: record.total_actual,
      status: 'Verified',
      attachment: record.receipt || 'Manual Entry',
      category: record.main_headers,
      description: record.particulars,
      source_type: record.receipt ? 'excel' : 'manual',
      budget_q1: record.budget_q1,
      budget_q2: record.budget_q2,
      budget_q3: record.budget_q3,
      budget_q4: record.budget_q4,
      actual_q1: record.actual_q1,
      actual_q2: record.actual_q2,
      actual_q3: record.actual_q3,
      actual_q4: record.actual_q4,
      receipt: record.receipt
    };
  } catch (error) {
    console.error(`Error fetching expense with ID ${id}:`, error);
    throw error;
  }
};