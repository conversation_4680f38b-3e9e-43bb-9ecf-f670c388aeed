import apiClient from '@/lib/apiClient';
import { GrantAPIResponse } from '@/types/profile';
import { json } from 'stream/consumers';

// Fetch all expenses
export const fetchAllExpenses = async () => {
  try {
    const response = await apiClient.get('/api/funding/v1/expenses/list/');
    return response.data;
  } catch (error) {
    console.error('fetchAllExpenses error:', error);
    throw error;
  }
};

export const fetchExpensesByGrant = async (grantId: string) => {
  console.log('Fetching expenses for grant ID:', grantId);
  const response = await apiClient.get(`/api/funding/v1/grants/${grantId}/expenses`)
  console.log("Inner response = ", JSON.stringify(response));
  return response.data;

}

// Fetch a single expense detail
export const fetchExpenseDetail = async (id: string) => {
  try {
    const response = await apiClient.get(`/api/funding/v1/expenses/${id}/`);
    return response.data;
  } catch (error) {
    console.error('fetchExpenseDetail error:', error);
    throw error;
  }
};
// Fetch all grants
export const getGrants = async (): Promise<GrantAPIResponse[]> => {
    const { data } = await apiClient.get("/api/v1/me/grants");
    return data;
};

export const getGrantss = async () => {
  try {
    const { data } = await apiClient.get("/api/funding/v1/expenses/grants/");
    return data;
  } catch (error: any) {
    console.error("getGrants error:", error);
    throw error;
  }
};

// Create a new expense
export const createExpense = async (expenseData: any) => {
  try {
    const response = await apiClient.post('/api/funding/v1/expenses/create/', expenseData);
    return response.data;
  } catch (error) {
    console.error('createExpense error:', error);
    throw error;
  }
};


// Bulk update expenses
export const bulkUpdateExpenses = async (expenseUpdates: any[]) => {
  try {
    const response = await apiClient.put('/api/funding/v1/expenses/bulk_update/', expenseUpdates);
    return response.data;
  } catch (error) {
    console.error('bulkUpdateExpenses error:', error);
    throw error;
  }
};