import axios from 'axios';
import apiClient from '@/lib/apiClient';
import { API_BASE_URL } from '@/config/constants';

export interface GrantSummary {
  totalGrantAmount: number;
  allocatedBudget: number;
  spentToDate: number;
  remainingBalance: number;
  grantStartDate: string;
  grantEndDate: string;
}

export interface ExpenseBreakdown {
  name: string;
  value: number;
  percentage: string;
  color?: string;
}

export interface MonthlyExpense {
  month: string;
  budget: number;
  actual: number;
}

export interface GrantFinancialSummary {
  id: number;
  grant_name: string;
  allocated_amount: number;
  disbursed_amount: number;
  utilized_amount: number;
  remaining_balance: number;
  start_date: string | null;
  end_date: string | null;
  quarterly_data: Array<{
    quarter: string;
    budget: number;
    actual: number;
  }>;
}

export interface ExpenseRecord {
  id: string;
  loggedDate: string;
  totalBudget: number;
  totalActualSpent: number;
  status: 'approved' | 'pending' | 'rejected' | string;
  attachment: string;
  category: string;
  description?: string;
  source_type?: 'excel' | 'manual' | 'grantmaker_excel' | string;
  receipt?: string | File | null;
  excel_data?: any;
  units?: string;
  frequency?: string;
  cost_per_unit?: number;
  budget_q1?: number;
  budget_q2?: number;
  budget_q3?: number;
  budget_q4?: number;
  actual_q1?: number;
  actual_q2?: number;
  actual_q3?: number;
  actual_q4?: number;
  is_parent?: boolean;
  is_child?: boolean;
  parent_id?: string;
  batch_id?: string;
  child_count?: number;
  children?: ExpenseRecord[];
  remarks?: string;
  rejection_notes?: string;
  is_frozen?: boolean;
}

export interface DisbursementSummary {
  totalDisbursed: number;
  remainingBalance: number;
  pendingDisbursements: number;
}

export interface UpcomingDisbursement {
  id: string;
  amount: number;
  date: string;
  grantName: string;
  status: 'Scheduled' | 'Pending' | 'Processing' | string;
}

export interface DisbursementHistory {
  id: string;
  date: string;
  amount: number;
  status: 'Disbursed' | 'Pending' | 'Failed' | string;
  remark: string;
  acknowledgement: 'Completed' | 'Send Receipt' | 'Pending' | string;
  grantName: string;
}

export interface MonthlyDisbursement {
  month: string;
  value: number;
}

const mockData = {
  grantSummary: {
    totalGrantAmount: 1000000,
    allocatedBudget: 800000,
    spentToDate: 450000,
    remainingBalance: 350000,
    grantStartDate: '2024-01-01',
    grantEndDate: '2024-12-31'
  },
  expenseBreakdown: [
    { name: 'Personnel', value: 200000, percentage: '40%', color: '#FF6B6B' },
    { name: 'Operations', value: 150000, percentage: '30%', color: '#4ECDC4' },
    { name: 'Programs', value: 100000, percentage: '20%', color: '#45B7D1' },
    { name: 'Other', value: 50000, percentage: '10%', color: '#96CEB4' }
  ],
  monthlyExpenses: [
    { month: 'Jan', budget: 80000, actual: 75000 },
    { month: 'Feb', budget: 85000, actual: 82000 },
    { month: 'Mar', budget: 90000, actual: 88000 },
    { month: 'Apr', budget: 95000, actual: 90000 },
    { month: 'May', budget: 100000, actual: 95000 },
    { month: 'Jun', budget: 105000, actual: 100000 },
    { month: 'Jul', budget: 110000, actual: 105000 },
    { month: 'Aug', budget: 115000, actual: 110000 },
    { month: 'Sep', budget: 120000, actual: 115000 },
    { month: 'Oct', budget: 125000, actual: 120000 },
    { month: 'Nov', budget: 130000, actual: 125000 },
    { month: 'Dec', budget: 135000, actual: 130000 }
  ],
  expenseHistory: [
    {
      id: 'EXP-2024-001',
      loggedDate: '2024-01-15',
      totalBudget: 50000,
      totalActualSpent: 48000,
      status: 'Verified',
      attachment: 'expense-1.pdf',
      category: 'Personnel',
      source_type: 'manual',
      receipt: 'personnel-receipt-jan2024.pdf'
    },
    {
      id: 'EXP-2024-002',
      loggedDate: '2024-02-01',
      totalBudget: 45000,
      totalActualSpent: 43000,
      status: 'Pending',
      attachment: 'Manual Entry',
      category: 'Operations',
      source_type: 'manual',
      receipt: 'operations-receipt-feb2024.pdf'
    },
    {
      id: 'EXP-2024-003',
      loggedDate: '2024-02-15',
      totalBudget: 60000,
      totalActualSpent: 58000,
      status: 'Verified',
      attachment: 'expense-data.xlsx',
      category: 'Programs',
      source_type: 'excel',
      receipt: null,
      excel_data: [
        {
          particulars: 'Program Supplies',
          main_header: 'Programs',
          sub_headers: 'Materials',
          budget_quarterly: { Q1: 15000, Q2: 15000, Q3: 15000, Q4: 15000 },
          actuals_quarterly: { Q1: 14500, Q2: 14800, Q3: 14700, Q4: 14000 }
        }
      ]
    },
    {
      id: 'EXP-2024-004',
      loggedDate: '2024-03-01',
      totalBudget: 55000,
      totalActualSpent: 52000,
      status: 'Verified',
      attachment: 'expense-4.pdf',
      category: 'Personnel',
      source_type: 'manual'
    },
    {
      id: 'EXP-2024-005',
      loggedDate: '2024-03-15',
      totalBudget: 40000,
      totalActualSpent: 38000,
      status: 'Pending',
      attachment: 'Manual Entry',
      category: 'Other',
      source_type: 'manual'
    }
  ],
  disbursementSummary: {
    totalDisbursed: 800000,
    remainingBalance: 200000,
    pendingDisbursements: 5
  },
  upcomingDisbursements: [
    {
      id: 'PF-2024-001',
      amount: 100000,
      date: '2024-06-15',
      grantName: 'Tranche 1',
      status: 'Scheduled'
    },
    {
      id: 'PF-2024-002',
      amount: 75000,
      date: '2024-07-01',
      grantName: 'Tranche 2',
      status: 'Pending'
    },
    {
      id: 'PF-2024-003',
      amount: 125000,
      date: '2024-07-15',
      grantName: 'Tranche 3',
      status: 'Processing'
    },
    {
      id: 'PF-2024-004',
      amount: 50000,
      date: '2024-08-01',
      grantName: 'Tranche 4',
      status: 'Scheduled'
    },
    {
      id: 'PF-2024-005',
      amount: 50000,
      date: '2024-08-15',
      grantName: 'Tranche 5',
      status: 'Scheduled'
    }
  ],
  disbursementHistory: [
    {
      id: 'PF-2024-001',
      date: '2024-01-15',
      amount: 150000,
      status: 'Disbursed',
      remark: 'Initial Installment',
      acknowledgement: 'Completed',
      grantName: 'Tranche 1'
    },
    {
      id: 'PF-2024-002',
      date: '2024-02-01',
      amount: 100000,
      status: 'Disbursed',
      remark: 'Second Installment',
      acknowledgement: 'Completed',
      grantName: 'Tranche 2'
    },
    {
      id: 'PF-2024-003',
      date: '2024-02-15',
      amount: 200000,
      status: 'Disbursed',
      remark: 'Initial Installment',
      acknowledgement: 'Completed',
      grantName: 'Tranche 3'
    },
    {
      id: 'PF-2024-004',
      date: '2024-03-01',
      amount: 75000,
      status: 'Disbursed',
      remark: 'Second Installment',
      acknowledgement: 'Send Receipt',
      grantName: 'Tranche 4'
    },
    {
      id: 'PF-2024-005',
      date: '2024-03-15',
      amount: 75000,
      status: 'Disbursed',
      remark: 'Second Installment',
      acknowledgement: 'Pending',
      grantName: 'Tranche 5'
    }
  ],
  monthlyDisbursements: [
    { month: 'Jan', value: 150000 },
    { month: 'Feb', value: 300000 },
    { month: 'Mar', value: 150000 },
    { month: 'Apr', value: 0 },
    { month: 'May', value: 0 },
    { month: 'Jun', value: 0 }
  ]
};

export const fundingService = {
  formatCurrency(amount: number | string | undefined): string {
    if (amount === undefined || amount === null) return '-';
    const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount;
    if (isNaN(numAmount)) return '-';
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      maximumFractionDigits: 0
    }).format(numAmount);
  },
  async getGrantSummary(): Promise<GrantSummary> {
    try {
      const response = await axios.get(`${API_BASE_URL}/funding/summary`);
      return response.data;
    } catch (error) {
      console.warn('Failed to fetch grant summary, using mock data:', error);
      return mockData.grantSummary;
    }
  },

  async getGrantFinancialSummaries(): Promise<GrantFinancialSummary[]> {
    try {
      console.log('Fetching real grant financial data from backend...');

      // First, get all grants for the user
      const grantsResponse = await apiClient.get('/api/v1/me/grants');
      const grants = grantsResponse.data;

      console.log('✅ Fetched grants from backend:', grants);

      if (!grants || grants.length === 0) {
        console.warn('No grants found for user');
        return [];
      }

      // Also fetch all expenses to get comprehensive data
      const allExpensesResponse = await apiClient.get('/api/funding/v1/expenses/list/');
      const allExpenses = allExpensesResponse.data || [];

      console.log('✅ Fetched all expenses from backend:', allExpenses);

      // For each grant, calculate financial data from real backend records
      const grantFinancialSummaries: GrantFinancialSummary[] = [];

      for (const grant of grants) {
        try {
          // Helper function to safely convert to number
          const safeNumber = (value: any): number => {
            const num = Number(value);
            return isNaN(num) ? 0 : num;
          };

          // Filter expenses for this specific grant
          const grantExpenses = allExpenses.filter((expense: any) => expense.grant === grant.id);

          console.log(`📊 Processing grant ${grant.id} (${grant.grant_name}):`, {
            totalExpenses: grantExpenses.length,
            grantBudget: grant.annual_budget
          });

          // Calculate financial metrics from real expense data
          const allocated_amount = safeNumber(grant.annual_budget);

          // Calculate utilized amount from approved expenses (total_actual)
          const utilized_amount = grantExpenses
            .filter((expense: any) => expense.status === 'approved')
            .reduce((sum: number, expense: any) => sum + safeNumber(expense.total_actual), 0);

          // Calculate total budget from all expenses
          const total_budget_from_expenses = grantExpenses
            .reduce((sum: number, expense: any) => sum + safeNumber(expense.total_budget), 0);

          // Calculate disbursed amount from disbursement records or estimate
          let disbursed_amount = 0;
          try {
            // Try to fetch disbursement data for this grant
            const disbursementResponse = await apiClient.get(`/api/funding/v1/disbursements/?grant=${grant.id}`);
            const disbursements = disbursementResponse.data || [];
            disbursed_amount = disbursements
              .reduce((sum: number, disbursement: any) => sum + safeNumber(disbursement.received_amount), 0);
          } catch (disbursementError) {
            // If disbursement API fails, estimate as percentage of allocated
            disbursed_amount = allocated_amount * 0.8;
            console.log(`⚠️ Using estimated disbursed amount for grant ${grant.id}`);
          }

          // Calculate remaining balance
          const remaining_balance = allocated_amount - utilized_amount;

          // Calculate quarterly data from real expense records
          const quarterly_data = [
            {
              quarter: 'Q1',
              budget: grantExpenses.reduce((sum: number, expense: any) => sum + safeNumber(expense.budget_q1), 0),
              actual: grantExpenses.reduce((sum: number, expense: any) => sum + safeNumber(expense.actual_q1), 0)
            },
            {
              quarter: 'Q2',
              budget: grantExpenses.reduce((sum: number, expense: any) => sum + safeNumber(expense.budget_q2), 0),
              actual: grantExpenses.reduce((sum: number, expense: any) => sum + safeNumber(expense.actual_q2), 0)
            },
            {
              quarter: 'Q3',
              budget: grantExpenses.reduce((sum: number, expense: any) => sum + safeNumber(expense.budget_q3), 0),
              actual: grantExpenses.reduce((sum: number, expense: any) => sum + safeNumber(expense.actual_q3), 0)
            },
            {
              quarter: 'Q4',
              budget: grantExpenses.reduce((sum: number, expense: any) => sum + safeNumber(expense.budget_q4), 0),
              actual: grantExpenses.reduce((sum: number, expense: any) => sum + safeNumber(expense.actual_q4), 0)
            }
          ];

          console.log(`📈 Grant ${grant.id} financial summary:`, {
            allocated_amount,
            disbursed_amount,
            utilized_amount,
            remaining_balance,
            quarterly_totals: quarterly_data.map(q => ({ quarter: q.quarter, budget: q.budget, actual: q.actual }))
          });

          grantFinancialSummaries.push({
            id: grant.id,
            grant_name: grant.grant_name,
            allocated_amount,
            disbursed_amount,
            utilized_amount,
            remaining_balance,
            start_date: grant.start_date,
            end_date: grant.end_date,
            quarterly_data
          });

        } catch (grantError) {
          console.error(`❌ Error processing grant ${grant.id}:`, grantError);

          // Helper function to safely convert to number
          const safeNumber = (value: any): number => {
            const num = Number(value);
            return isNaN(num) ? 0 : num;
          };

          // Add grant with basic info but no expense data
          const allocated_amount = safeNumber(grant.annual_budget);
          grantFinancialSummaries.push({
            id: grant.id,
            grant_name: grant.grant_name,
            allocated_amount,
            disbursed_amount: 0,
            utilized_amount: 0,
            remaining_balance: allocated_amount,
            start_date: grant.start_date,
            end_date: grant.end_date,
            quarterly_data: [
              { quarter: 'Q1', budget: 0, actual: 0 },
              { quarter: 'Q2', budget: 0, actual: 0 },
              { quarter: 'Q3', budget: 0, actual: 0 },
              { quarter: 'Q4', budget: 0, actual: 0 }
            ]
          });
        }
      }

      console.log('🎉 Final real grant financial summaries from backend:', grantFinancialSummaries);
      return grantFinancialSummaries;

    } catch (error) {
      console.error('❌ Failed to fetch real grant financial data:', error);
      console.log('🔄 Backend unavailable, returning empty array to show real state');

      // Return empty array to show actual backend state
      return [];
    }
  },

  async getExpenseBreakdown(): Promise<ExpenseBreakdown[]> {
    try {
      const response = await axios.get(`${API_BASE_URL}/funding/expense-breakdown`);
      return response.data;
    } catch (error) {
      console.warn('Failed to fetch expense breakdown, using mock data:', error);
      return mockData.expenseBreakdown;
    }
  },

  async getMonthlyExpenses(): Promise<MonthlyExpense[]> {
    try {
      const response = await axios.get(`${API_BASE_URL}/funding/monthly-expenses`);
      return response.data;
    } catch (error) {
      console.warn('Failed to fetch monthly expenses, using mock data:', error);
      return mockData.monthlyExpenses;
    }
  },

  async getExpenseHistory(): Promise<ExpenseRecord[]> {
    try {
      // Make sure we're using the correct API endpoint format
      // Use apiClient which handles authentication and proper API paths
      const response = await apiClient.get('/api/funding/v1/expenses/');

      // Transform the API data to match our ExpenseRecord interface
      const allExpenses = response.data.map((item: any) => {
        // For debugging
        console.log('API response item:', item);

        return {
          id: item.id.toString(),
          loggedDate: item.expense_date || new Date().toISOString().split('T')[0],
          totalBudget: item.total_budget || 0,
          totalActualSpent: item.total_actual || 0,
          status: 'Verified', // Default status
          // Fix attachment display - use the actual file name when available
          attachment: item.receipt ? item.receipt : (item.source_type === 'manual' ? 'Manual Entry' : ''),
          category: item.main_headers || '',
          description: item.particulars || '',
          source_type: item.source_type || 'manual',
          units: item.units || '',
          frequency: item.frequency || '',
          cost_per_unit: item.cost_per_unit || 0,
          budget_q1: item.budget_q1 || 0,
          budget_q2: item.budget_q2 || 0,
          budget_q3: item.budget_q3 || 0,
          budget_q4: item.budget_q4 || 0,
          actual_q1: item.actual_q1 || 0,
          actual_q2: item.actual_q2 || 0,
          actual_q3: item.actual_q3 || 0,
          actual_q4: item.actual_q4 || 0,
          receipt: item.receipt || null,
          is_parent: item.is_parent === 'true' || item.is_parent === true,
          is_child: item.is_child === 'true' || item.is_child === true,
          parent_id: item.parent_id?.toString() || undefined,
          batch_id: item.batch_id || undefined,
          child_count: item.child_count ? parseInt(item.child_count) : undefined,
          remarks: item.remarks || '',
          is_frozen: item.is_frozen === 'true' || item.is_frozen === true
        };
      });

      // Group child expenses under their parent expenses
      const parentExpenses = allExpenses.filter((expense: ExpenseRecord) => expense.is_parent);
      const childExpenses = allExpenses.filter((expense: ExpenseRecord) => expense.is_child);
      const standaloneExpenses = allExpenses.filter((expense: ExpenseRecord) => !expense.is_parent && !expense.is_child);

      // Organize children under their parents
      parentExpenses.forEach((parent: ExpenseRecord) => {
        parent.children = childExpenses.filter((child: ExpenseRecord) =>
          child.parent_id === parent.id || child.batch_id === parent.batch_id
        );
      });

      // Return only parent expenses and standalone expenses (no children)
      const result = [...parentExpenses, ...standaloneExpenses];

      // Sort by date, newest first
      result.sort((a, b) => new Date(b.loggedDate).getTime() - new Date(a.loggedDate).getTime());

      return result;
    } catch (error: any) {
      // Check if the error is due to receiving HTML instead of JSON
      if (error.response && error.response.headers['content-type']?.includes('text/html')) {
        console.error('Received HTML instead of JSON. API endpoint may be incorrect:', error);
        // Log the actual response for debugging
        console.error('Response data:', error.response.data);
      } else {
        console.warn('Failed to fetch expense history, using mock data:', error);
      }
      return mockData.expenseHistory;
    }
  },

  async processExcelFile(file: File): Promise<any[]> {
    try {
      console.log(`Processing Excel file: ${file.name}, size: ${file.size} bytes`);

      // In a real implementation, this would send the file to the backend for processing
      // const formData = new FormData();
      // formData.append('file', file);
      // const response = await axios.post(`${API_BASE_URL}/funding/v1/expenses/process-excel`, formData, {
      //   headers: {
      //     'Content-Type': 'multipart/form-data',
      //   },
      // });
      // return response.data;

      // For now, simulate processing with a delay
      return new Promise((resolve) => {
        setTimeout(() => {
          // Use the file name to customize the mock data (just for demonstration)
          const fileName = file.name.toLowerCase();
          let processedData;

          if (fileName.includes('salary') || fileName.includes('hr')) {
            processedData = [
              {
                particulars: 'Staff Salaries',
                main_header: 'Human Resources',
                sub_headers: 'Permanent Staff',
                budget_quarterly: { Q1: 150000, Q2: 150000, Q3: 150000, Q4: 150000 },
                actuals_quarterly: { Q1: 145000, Q2: 150000, Q3: 148000, Q4: 0 }
              },
              {
                particulars: 'Consultant Fees',
                main_header: 'Human Resources',
                sub_headers: 'Consultants',
                budget_quarterly: { Q1: 50000, Q2: 50000, Q3: 50000, Q4: 50000 },
                actuals_quarterly: { Q1: 45000, Q2: 55000, Q3: 48000, Q4: 0 }
              }
            ];
          } else if (fileName.includes('admin') || fileName.includes('office')) {
            processedData = [
              {
                particulars: 'Office Rent',
                main_header: 'Administrative',
                sub_headers: 'Facilities',
                budget_quarterly: { Q1: 75000, Q2: 75000, Q3: 75000, Q4: 75000 },
                actuals_quarterly: { Q1: 75000, Q2: 75000, Q3: 75000, Q4: 0 }
              },
              {
                particulars: 'Office Supplies',
                main_header: 'Administrative',
                sub_headers: 'Supplies',
                budget_quarterly: { Q1: 25000, Q2: 25000, Q3: 25000, Q4: 25000 },
                actuals_quarterly: { Q1: 22000, Q2: 24000, Q3: 26000, Q4: 0 }
              }
            ];
          } else {
            // Default data
            processedData = [
              {
                particulars: 'Program Expenses',
                main_header: 'Programs',
                sub_headers: 'General',
                budget_quarterly: { Q1: 100000, Q2: 100000, Q3: 100000, Q4: 100000 },
                actuals_quarterly: { Q1: 95000, Q2: 98000, Q3: 97000, Q4: 0 }
              },
              {
                particulars: 'Travel Expenses',
                main_header: 'Programs',
                sub_headers: 'Travel',
                budget_quarterly: { Q1: 50000, Q2: 50000, Q3: 50000, Q4: 50000 },
                actuals_quarterly: { Q1: 45000, Q2: 48000, Q3: 52000, Q4: 0 }
              }
            ];
          }

          resolve(processedData);
        }, 1000);
      });
    } catch (error) {
      console.error('Error processing Excel file:', error);
      throw new Error('Failed to process Excel file');
    }
  },

  async getDisbursementSummary(): Promise<DisbursementSummary> {
    try {
      const response = await axios.get(`${API_BASE_URL}/funding/disbursement-summary`);
      return response.data;
    } catch (error) {
      console.warn('Failed to fetch disbursement summary, using mock data:', error);
      return mockData.disbursementSummary;
    }
  },

  async getUpcomingDisbursements(): Promise<UpcomingDisbursement[]> {
    try {
      const response = await axios.get(`${API_BASE_URL}/funding/upcoming-disbursements`);
      return response.data;
    } catch (error) {
      console.warn('Failed to fetch upcoming disbursements, using mock data:', error);
      return mockData.upcomingDisbursements;
    }
  },

  async getDisbursementHistory(): Promise<DisbursementHistory[]> {
    try {
      // Use the real API endpoint for disbursements
      const response = await apiClient.get('/api/funding/v1/disbursements/');
      console.log('✅ Fetched real disbursement data:', response.data);

      // Transform the API data to match our DisbursementHistory interface
      return response.data.map((item: any) => ({
        id: item.id,
        grantName: item.grant?.grant_name || 'Unknown Grant',
        amount: item.received_amount || item.scheduled_amount,
        date: item.payment_received_date || item.scheduled_payment_date,
        status: item.received_amount ? 'Received' : 'Pending',
        description: `Disbursement for ${item.grant?.grant_name || 'Grant'}`,
        reference: `DISB-${item.id}`
      }));
    } catch (error) {
      console.warn('Failed to fetch disbursement history, using mock data:', error);
      return mockData.disbursementHistory;
    }
  },

  async getMonthlyDisbursements(): Promise<MonthlyDisbursement[]> {
    try {
      const response = await axios.get(`${API_BASE_URL}/funding/monthly-disbursements`);
      return response.data;
    } catch (error) {
      console.warn('Failed to fetch monthly disbursements, using mock data:', error);
      return mockData.monthlyDisbursements;
    }
  },

  async submitExpense(data: FormData): Promise<void> {
    try {
      // Check if this is a manual entry or Excel upload
      const sourceType = data.get('source_type');

      // Get the current user's grant ID
      // In a real implementation, this would come from the user's context or profile
      // For now, we'll use a default grant ID of 1
      const grantId = 1; // This should be dynamically fetched in a real implementation
      data.append('grant', grantId.toString());

      // Generate a unique batch ID for this submission to group related expenses
      // Use the batch_id if it's already provided, otherwise generate a new one
      const batchId = data.get('batch_id') as string || `BATCH-${Date.now()}-${Math.floor(Math.random() * 1000)}`;
      if (!data.get('batch_id')) {
        data.append('batch_id', batchId);
      }
      const currentDate = new Date().toISOString().split('T')[0];

      // Add batch_id to the form data
      data.append('batch_id', batchId);

      // Format the data for the backend
      if (sourceType === 'excel') {
        // For Excel uploads, we need to process the file
        const file = data.get('file') as File;
        if (!file) {
          throw new Error('No file provided for Excel upload');
        }

        // Process the Excel file to extract data
        const processedData = await this.processExcelFile(file);

        // Calculate the total budget and actual for all rows combined
        const totalBudgetAll = processedData.reduce((sum: number, row: any) => {
          return sum + Object.values(row.budget_quarterly).reduce((rowSum: number, val: any) => rowSum + Number(val), 0);
        }, 0);

        const totalActualAll = processedData.reduce((sum: number, row: any) => {
          return sum + Object.values(row.actuals_quarterly).reduce((rowSum: number, val: any) => rowSum + Number(val), 0);
        }, 0);

        // Create a parent expense record that will contain all rows
        const parentExpenseData = new FormData();
        parentExpenseData.append('grant', grantId.toString());
        parentExpenseData.append('particulars', `Excel Import: ${file.name}`);
        parentExpenseData.append('main_headers', processedData[0]?.main_header || 'Multiple Categories');
        parentExpenseData.append('sub_headers', 'Multiple Items');
        parentExpenseData.append('total_budget', totalBudgetAll.toString());
        parentExpenseData.append('total_actual', totalActualAll.toString());
        parentExpenseData.append('source_type', 'excel');
        parentExpenseData.append('expense_date', currentDate);
        parentExpenseData.append('is_parent', 'true');
        parentExpenseData.append('batch_id', batchId);
        parentExpenseData.append('child_count', processedData.length.toString());

        // Add the file as receipt if available
        if (file) {
          parentExpenseData.append('receipt', file);
        }

        // Submit the parent expense entry to the backend
        let parentId = '';
        try {
          const response = await apiClient.post('/api/funding/v1/expenses/', parentExpenseData, {
            headers: {
              'Content-Type': 'multipart/form-data',
            },
            timeout: 30000,
          });

          if (response.data && response.data.id) {
            parentId = response.data.id.toString();
            console.log('Parent expense created with ID:', parentId);
          } else {
            console.warn('Parent expense created but no ID returned:', response.data);
          }
        } catch (err) {
          console.error('Failed to create parent expense record:', err);
        }

        // Now create child expense entries for each row in the Excel data
        for (const row of processedData) {
          const expenseData = new FormData();

          // Add common fields
          expenseData.append('grant', grantId.toString());
          expenseData.append('particulars', row.particulars);
          expenseData.append('main_headers', row.main_header);
          expenseData.append('sub_headers', row.sub_headers);

          // Add budget quarterly data
          expenseData.append('budget_q1', row.budget_quarterly.Q1.toString());
          expenseData.append('budget_q2', row.budget_quarterly.Q2.toString());
          expenseData.append('budget_q3', row.budget_quarterly.Q3.toString());
          expenseData.append('budget_q4', row.budget_quarterly.Q4.toString());

          // Add actuals quarterly data
          expenseData.append('actual_q1', row.actuals_quarterly.Q1.toString());
          expenseData.append('actual_q2', row.actuals_quarterly.Q2.toString());
          expenseData.append('actual_q3', row.actuals_quarterly.Q3.toString());
          expenseData.append('actual_q4', row.actuals_quarterly.Q4.toString());

          // Calculate totals
          const totalBudget = Object.values(row.budget_quarterly).reduce((sum: number, val: any) => sum + Number(val), 0);
          const totalActual = Object.values(row.actuals_quarterly).reduce((sum: number, val: any) => sum + Number(val), 0);

          expenseData.append('total_budget', totalBudget.toString());
          expenseData.append('total_actual', totalActual.toString());

          // Add source type and expense date
          expenseData.append('source_type', 'excel');
          expenseData.append('expense_date', currentDate);
          expenseData.append('is_child', 'true');
          expenseData.append('batch_id', batchId);

          // Link to parent if we have a parent ID
          if (parentId) {
            expenseData.append('parent_id', parentId);
          }

          // Add the file as receipt if available
          if (file) {
            expenseData.append('receipt', file);
          }

          // Submit each child expense entry to the backend
          try {
            const response = await apiClient.post('/api/funding/v1/expenses/', expenseData, {
              headers: {
                'Content-Type': 'multipart/form-data',
              },
              timeout: 30000,
            });

            console.log('Child expense submission successful:', response.data);
          } catch (err) {
            console.error('Failed to create child expense record:', err);
          }
        }
      } else {
        // For manual entries
        // Get the expense rows from the form data
        const expenseRowsJson = data.get('expense_rows');
        if (!expenseRowsJson) {
          throw new Error('No expense data provided for manual entry');
        }

        let expenseRows;
        try {
          expenseRows = JSON.parse(expenseRowsJson as string);
        } catch (parseError) {
          console.error('Failed to parse expense rows JSON:', parseError);
          throw new Error('Invalid expense data format. Please check your input.');
        }

        // If there's only one row, process it normally without parent/child relationship
        if (expenseRows.length === 1) {
          const row = expenseRows[0];
          const expenseData = new FormData();

          // Add common fields
          expenseData.append('grant', grantId.toString());
          expenseData.append('particulars', row.particulars);
          expenseData.append('main_headers', row.main_header);
          expenseData.append('sub_headers', row.sub_headers);

          // Add budget quarterly data
          expenseData.append('budget_q1', row.budget_quarterly.Q1.toString());
          expenseData.append('budget_q2', row.budget_quarterly.Q2.toString());
          expenseData.append('budget_q3', row.budget_quarterly.Q3.toString());
          expenseData.append('budget_q4', row.budget_quarterly.Q4.toString());

          // Add actuals quarterly data
          expenseData.append('actual_q1', row.actuals_quarterly.Q1.toString());
          expenseData.append('actual_q2', row.actuals_quarterly.Q2.toString());
          expenseData.append('actual_q3', row.actuals_quarterly.Q3.toString());
          expenseData.append('actual_q4', row.actuals_quarterly.Q4.toString());

          // Calculate totals
          const totalBudget = Object.values(row.budget_quarterly).reduce((sum: number, val: any) => sum + Number(val), 0);
          const totalActual = Object.values(row.actuals_quarterly).reduce((sum: number, val: any) => sum + Number(val), 0);

          expenseData.append('total_budget', totalBudget.toString());
          expenseData.append('total_actual', totalActual.toString());

          // Add source type and expense date
          expenseData.append('source_type', 'manual');
          expenseData.append('expense_date', currentDate);

          // Add receipt if available
          if (row.receipt) {
            expenseData.append('receipt', row.receipt);
          }

          // Submit the expense entry to the backend
          try {
            const response = await apiClient.post('/api/funding/v1/expenses/', expenseData, {
              headers: {
                'Content-Type': 'multipart/form-data',
              },
              timeout: 30000,
            });

            console.log('Single expense submission successful:', response.data);
          } catch (err) {
            console.error('Failed to create expense record:', err);
          }
        } else if (expenseRows.length > 1) {
          // For multiple rows, create a parent-child relationship

          // Calculate the total budget and actual for all rows combined
          const totalBudgetAll = expenseRows.reduce((sum: number, row: any) => {
            return sum + Object.values(row.budget_quarterly).reduce((rowSum: number, val: any) => rowSum + Number(val), 0);
          }, 0);

          const totalActualAll = expenseRows.reduce((sum: number, row: any) => {
            return sum + Object.values(row.actuals_quarterly).reduce((rowSum: number, val: any) => rowSum + Number(val), 0);
          }, 0);

          // Create a parent expense record that will contain all rows
          const parentExpenseData = new FormData();
          parentExpenseData.append('grant', grantId.toString());
          parentExpenseData.append('particulars', 'Manual Entry: Multiple Items');
          parentExpenseData.append('main_headers', expenseRows[0]?.main_header || 'Multiple Categories');
          parentExpenseData.append('sub_headers', 'Multiple Items');
          parentExpenseData.append('total_budget', totalBudgetAll.toString());
          parentExpenseData.append('total_actual', totalActualAll.toString());
          parentExpenseData.append('source_type', 'manual');
          parentExpenseData.append('expense_date', currentDate);
          parentExpenseData.append('is_parent', 'true');
          parentExpenseData.append('batch_id', batchId);
          parentExpenseData.append('child_count', expenseRows.length.toString());

          // Submit the parent expense entry to the backend
          let parentId = '';
          try {
            const response = await apiClient.post('/api/funding/v1/expenses/', parentExpenseData, {
              headers: {
                'Content-Type': 'multipart/form-data',
              },
              timeout: 30000,
            });

            if (response.data && response.data.id) {
              parentId = response.data.id.toString();
              console.log('Parent expense created with ID:', parentId);
            } else {
              console.warn('Parent expense created but no ID returned:', response.data);
            }
          } catch (err) {
            console.error('Failed to create parent expense record:', err);
          }

          // Now create child expense entries for each manual row
          for (const row of expenseRows) {
            const expenseData = new FormData();

            // Add common fields
            expenseData.append('grant', grantId.toString());
            expenseData.append('particulars', row.particulars);
            expenseData.append('main_headers', row.main_header);
            expenseData.append('sub_headers', row.sub_headers);

            // Add budget quarterly data
            expenseData.append('budget_q1', row.budget_quarterly.Q1.toString());
            expenseData.append('budget_q2', row.budget_quarterly.Q2.toString());
            expenseData.append('budget_q3', row.budget_quarterly.Q3.toString());
            expenseData.append('budget_q4', row.budget_quarterly.Q4.toString());

            // Add actuals quarterly data
            expenseData.append('actual_q1', row.actuals_quarterly.Q1.toString());
            expenseData.append('actual_q2', row.actuals_quarterly.Q2.toString());
            expenseData.append('actual_q3', row.actuals_quarterly.Q3.toString());
            expenseData.append('actual_q4', row.actuals_quarterly.Q4.toString());

            // Calculate totals
            const totalBudget = Object.values(row.budget_quarterly).reduce((sum: number, val: any) => sum + Number(val), 0);
            const totalActual = Object.values(row.actuals_quarterly).reduce((sum: number, val: any) => sum + Number(val), 0);

            expenseData.append('total_budget', totalBudget.toString());
            expenseData.append('total_actual', totalActual.toString());

            // Add source type and expense date
            expenseData.append('source_type', 'manual');
            expenseData.append('expense_date', currentDate);
            expenseData.append('is_child', 'true');
            expenseData.append('batch_id', batchId);

            // Link to parent if we have a parent ID
            if (parentId) {
              expenseData.append('parent_id', parentId);
            }

            // Add receipt if available
            if (row.receipt) {
              expenseData.append('receipt', row.receipt);
            }

            // Submit each child expense entry to the backend
            try {
              const response = await apiClient.post('/api/funding/v1/expenses/', expenseData, {
                headers: {
                  'Content-Type': 'multipart/form-data',
                },
                timeout: 30000,
              });

              console.log('Child expense submission successful:', response.data);
            } catch (err) {
              console.error('Failed to create child expense record:', err);
            }
          }
        }
      }
    } catch (error) {
      console.error('Failed to submit expense:', error);

      // Improved error handling with more detailed messages
      if (error instanceof Error) {
        // If it's a standard Error object, use its message
        throw new Error(`Failed to submit expense: ${error.message}`);
      } else if (error && typeof error === 'object') {
        // If it's an object but not an Error instance, try to extract useful information
        const errorMessage = (error as any).message || error.toString();
        throw new Error(`Failed to submit expense: ${errorMessage}`);
      } else {
        // Fallback for unexpected error types
        throw new Error('Failed to submit expense. Please try again later.');
      }
    }
  },

  // Helper function to handle API errors
  handleApiError(err: any): never {
    if (err.response) {
      // The server responded with a status code outside the 2xx range
      const contentType = err.response.headers['content-type'] || '';

      if (contentType.includes('text/html')) {
        console.error('Received HTML instead of JSON. API endpoint may be incorrect or authentication required.');
        // Log the first part of the HTML response for debugging
        if (typeof err.response.data === 'string') {
          console.error('HTML response preview:', err.response.data.substring(0, 200));
        }
        throw new Error(`Server error: ${err.response.status}. The API endpoint may be incorrect or authentication required.`);
      } else if (contentType.includes('application/json')) {
        console.error('Error response data:', err.response.data);
        // Safely extract error message from JSON response
        const errorMessage = err.response.data && typeof err.response.data === 'object' && err.response.data.message
          ? err.response.data.message
          : `Server error: ${err.response.status}`;
        throw new Error(errorMessage);
      } else {
        console.error('Unexpected content type in error response:', contentType);
        throw new Error(`Server error: ${err.response.status}. Unexpected response format.`);
      }
    } else if (err.request) {
      // The request was made but no response was received
      console.error('No response received:', err.request);
      throw new Error('No response received from server. Please check your network connection.');
    } else {
      // Something happened in setting up the request
      console.error('Request setup error:', err.message);
      throw err;
    }
  },

  // Helper function to format currency (second implementation)
  formatCurrencyAlt(amount: number): string {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      maximumFractionDigits: 0
    }).format(amount);
  },

  // Helper function to calculate percentage
  calculatePercentage(value: number, total: number): string {
    return `${Math.round((value / total) * 100)}%`;
  }
};
