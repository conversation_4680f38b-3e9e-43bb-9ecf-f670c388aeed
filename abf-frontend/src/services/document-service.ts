import apiClient from "@/lib/apiClient";
import { getPresignedUrl, uploadFileToS3 } from "@/lib/s3Uploader";
import { DocumentType, DocumentTypeListResponse } from "@/types/profile";
import { Cone } from "lucide-react";

export interface DocumentUploadResponse {
  status: "SUCCESS" | "ERROR";
  message: string;
  data: Document,
  documentId?: string;
  documentUrl?: string;
}

export interface Document {
  id: number | null;
  name: string;
  attachment_type: string;
  status: "PENDING" | "VERIFIED" | "REJECTED" | "NOT_UPLOADED";
  objectKey: string | null;
  uploadedAt: string;
  url?: string;
  comments?: string;
}

export const uploadDocument = async (
  file: File,
  documentType: string,
  docId: number | null
): Promise<DocumentUploadResponse> => {
  try {
    // Create form data for file upload
    const formData = new FormData();
    formData.append("file", file);
    formData.append("documentType", documentType);

    const { upload_url: uploadUrl, file_url: fileUrl, object_key: objectKey } = await getPresignedUrl(file);


    try {
      const response = await uploadFileToS3(file, uploadUrl)

      const attachmentData = {
        object_key: objectKey,
        attachment_type: documentType,
        original_filename: file.name,
      }


      let url = "";
      console.log("DOC id " + docId);
      let uploadAttachmentResponse = null;
      if (docId !== null) {
      url = `/api/profiles/v1/attachments/${documentType}/`;
      uploadAttachmentResponse = await apiClient.patch(url, attachmentData);

      } else {
      url = `/api/profiles/v1/attachments/`;
      uploadAttachmentResponse = await apiClient.post(url, attachmentData);
      }


      console.log("Upload document response = " + JSON.stringify(uploadAttachmentResponse));
      return uploadAttachmentResponse.data;

    } catch(error) {
      console.log("ERror = " + error);

    }
  } catch (error) {
    console.error("Error uploading document:", error);
    return {
      success: false,
      message: error instanceof Error ? error.message : "Failed to upload document",
    };
  }
};

export const verifyDocument = async (
  documentId: string,
  status: "verified" | "pending" | "REJECTED",
  comments?: string
): Promise<{ success: boolean; message: string }> => {
  try {
    // In a real implementation, this would call the actual API endpoint
    // For now, we'll simulate a successful verification with a timeout
    
    // Simulate API call with a delay
    return new Promise((resolve) => {
      setTimeout(() => {
        // Mock successful response
        resolve({
          success: true,
          message: `Document status updated to ${status}`
        });
      }, 1000);
    });
    
    // Real implementation would look like this:
    // const response = await apiClient.put(`/api/documents/${documentId}/verify`, {
    //   status,
    //   comments
    // });
    // return response.data;
  } catch (error) {
    console.error("Error verifying document:", error);
    return {
      success: false,
      message: error instanceof Error ? error.message : "Failed to verify document",
    };
  }
};

export const getDocuments = async (): Promise<Document[]> => {
  try {
    // In a real implementation, this would call the actual API endpoint
    // For now, we'll return mock data
 
    const response = await apiClient.get("/api/profiles/v1/attachments");
    return response.data;
  } catch (error) {
    console.error("Error fetching documents:", error);
    return [];
  }
};

export const getDocumentTypes = async (): Promise<DocumentTypeListResponse> => {
  
  const response = await apiClient.get("/api/profiles/v1/attachment-types/");
  return response.data;

}