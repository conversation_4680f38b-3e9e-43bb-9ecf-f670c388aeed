'use client';

import React, { createContext, useContext, useState, ReactNode } from 'react';

interface ExpenseRow {
  id: number;
  particulars: string;
  main_header: string;
  sub_header: string;
  units: string;
  frequency: string;
  cost_per_unit: string;
  activity_description: string;
  budget_quarterly: Record<string, number>;
  actual_quarterly: Record<string, number>;
  total_budget: number;
  total_grant_budget: number;
  total_actual: number;
  grant: string;
  remarks: string;
}

interface QuarterlyTotals {
  'Apr-Jun': { budget: number; actual: number };
  'Jul-Sep': { budget: number; actual: number };
  'Oct-Dec': { budget: number; actual: number };
  'Jan-Mar': { budget: number; actual: number };
}

interface ExpenseDataContextType {
  excelData: ExpenseRow[];
  setExcelData: (data: ExpenseRow[]) => void;
  getQuarterlyTotals: () => QuarterlyTotals;
  getQuarterlyChartData: () => Array<{ quarter: string; budget: number; actual: number }>;
}

const ExpenseDataContext = createContext<ExpenseDataContextType | undefined>(undefined);

export const useExpenseData = () => {
  const context = useContext(ExpenseDataContext);
  if (context === undefined) {
    throw new Error('useExpenseData must be used within an ExpenseDataProvider');
  }
  return context;
};

interface ExpenseDataProviderProps {
  children: ReactNode;
}

export const ExpenseDataProvider: React.FC<ExpenseDataProviderProps> = ({ children }) => {
  const [excelData, setExcelData] = useState<ExpenseRow[]>([]);

  const getQuarterlyTotals = (): QuarterlyTotals => {
    const totals: QuarterlyTotals = {
      'Apr-Jun': { budget: 0, actual: 0 },
      'Jul-Sep': { budget: 0, actual: 0 },
      'Oct-Dec': { budget: 0, actual: 0 },
      'Jan-Mar': { budget: 0, actual: 0 }
    };

    excelData.forEach(row => {
      // Use quarter keys directly
      const quarterKeys = ['Apr-Jun', 'Jul-Sep', 'Oct-Dec', 'Jan-Mar'];

      quarterKeys.forEach(quarterKey => {
        const budget = row.budget_quarterly?.[quarterKey] || 0;
        const actual = row.actual_quarterly?.[quarterKey] || 0;

        totals[quarterKey as keyof QuarterlyTotals].budget += budget;
        totals[quarterKey as keyof QuarterlyTotals].actual += actual;
      });
    });

    return totals;
  };

  const getQuarterlyChartData = () => {
    const totals = getQuarterlyTotals();

    return [
      { quarter: 'Apr-Jun', budget: totals['Apr-Jun'].budget, actual: totals['Apr-Jun'].actual },
      { quarter: 'Jul-Sep', budget: totals['Jul-Sep'].budget, actual: totals['Jul-Sep'].actual },
      { quarter: 'Oct-Dec', budget: totals['Oct-Dec'].budget, actual: totals['Oct-Dec'].actual },
      { quarter: 'Jan-Mar', budget: totals['Jan-Mar'].budget, actual: totals['Jan-Mar'].actual }
    ];
  };

  const value: ExpenseDataContextType = {
    excelData,
    setExcelData,
    getQuarterlyTotals,
    getQuarterlyChartData
  };

  return (
    <ExpenseDataContext.Provider value={value}>
      {children}
    </ExpenseDataContext.Provider>
  );
};
