"use client";

import { useEffect, useMemo, useRef, useState } from "react";
import { useRouter } from "next/navigation";
import { format, formatDistanceToNow } from "date-fns";
import {
  MessageSquare,
  Filter,
  ArrowDown,
  Paperclip,
  Eye,
  Lock,
  Unlock,
  Search,
  AlertCircle,
} from "lucide-react";
import { toast } from "sonner";
import SupportTabsLayout from "../../SupportTabsLayout";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuCheckboxItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";
import {
  fetchAllTickets,
  getTicketUpdates,
  sendTicketUpdate,
  updateTicketStatus,
  getTicketDetails,
} from "@/services/supportTicket.service";

const STATUSES = ["open", "under review", "resolved"];

export default function GrantMakerChatPage() {
  const router = useRouter();
  const [tickets, setTickets] = useState<any[]>([]);
  const [selected, setSelected] = useState<any | null>(null);
  const [timelineMap, setTimelineMap] = useState<Record<number, any[]>>({});
  const [newMessage, setNewMessage] = useState("");
  const [file, setFile] = useState<File | null>(null);
  const [search, setSearch] = useState("");
  const [statusFilter, setStatusFilter] = useState<string[]>([]);
  const [isSending, setIsSending] = useState(false);
  const [newMsgAlert, setNewMsgAlert] = useState(false);
  const [showScrollButton, setShowScrollButton] = useState(false);
  const [updatingStatusId, setUpdatingStatusId] = useState<number | null>(null);

  const chatRef = useRef<HTMLDivElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const pollingRef = useRef<NodeJS.Timeout | null>(null);
  const lastUpdateIdRef = useRef<Record<number, number>>({});
  const lastStatusCountRef = useRef<Record<number, number>>({});

  const scrollToBottom = () => {
    setTimeout(() => {
      chatRef.current?.scrollTo({ top: chatRef.current.scrollHeight, behavior: "smooth" });
      setNewMsgAlert(false);
    }, 100);
  };

  const buildTimeline = (ticket: any, updates: any[]) => {
    const timeline = [];

    if (ticket?.description) {
      timeline.push({
        id: "desc",
        type: "message",
        update_text: ticket.description,
        updated_at: ticket.created_at,
        user: {
          type: { code: "GRANTEE" },
          first_name: ticket.created_by_first_name,
          last_name: ticket.created_by_last_name,
          email: ticket.created_by_email,
        },
        attachments: ticket.attachments || [],
      });
    }

    (ticket.status_history || []).forEach((entry: any, idx: number) => {
      timeline.push({
        type: "status",
        id: `status-${entry.id}-${idx}`,
        updated_at: entry.changed_at,
        to_status: entry.to_status,
        user: entry.changed_by,
      });
    });

    updates.forEach((u: any) => timeline.push({ type: "message", ...u }));

    return timeline.sort((a, b) => new Date(a.updated_at).getTime() - new Date(b.updated_at).getTime());
  };

  useEffect(() => {
    (async () => {
      const all = await fetchAllTickets();
      setTickets(all);
      if (all.length > 0) setSelected(all[0]);

      const map: Record<number, any[]> = {};
      for (const t of all) {
        const [updates, latestTicket] = await Promise.all([
          getTicketUpdates(t.id),
          getTicketDetails(t.id),
        ]);
        map[t.id] = buildTimeline(latestTicket, updates);
        lastUpdateIdRef.current[t.id] = updates.at(-1)?.id || 0;
        lastStatusCountRef.current[t.id] = latestTicket.status_history?.length || 0;
      }
      setTimelineMap(map);
    })();
  }, []);

  useEffect(() => {
    if (!selected?.id) return;

    pollingRef.current = setInterval(async () => {
      const [updates, latestTicket] = await Promise.all([
        getTicketUpdates(selected.id),
        getTicketDetails(selected.id),
      ]);

      const lastUpdateId = lastUpdateIdRef.current[selected.id] || 0;
      const nowUpdateId = updates.at(-1)?.id || 0;

      const lastStatusCount = lastStatusCountRef.current[selected.id] || 0;
      const nowStatusCount = latestTicket.status_history?.length || 0;

      const hasNewUpdate = nowUpdateId !== lastUpdateId;
      const hasNewStatus = nowStatusCount !== lastStatusCount;

      if (hasNewUpdate || hasNewStatus) {
        lastUpdateIdRef.current[selected.id] = nowUpdateId;
        lastStatusCountRef.current[selected.id] = nowStatusCount;

        const newTimeline = buildTimeline(latestTicket, updates);
        setTimelineMap((prev) => ({ ...prev, [selected.id]: newTimeline }));

        const isAtBottom =
          chatRef.current &&
          chatRef.current.scrollHeight - chatRef.current.scrollTop - chatRef.current.clientHeight < 100;

        if (isAtBottom) scrollToBottom();
        else setNewMsgAlert(true);

        if (hasNewUpdate && !hasNewStatus) {
          toast.success(
            latestTicket.status === "resolved"
              ? "You have received a new message. But this ticket is resolved."
              : "You have received a new message. Your ticket is open."
          );
        }

        if (hasNewStatus && !hasNewUpdate) {
          const latestStatus = latestTicket.status_history?.at(-1)?.to_status;
          toast.success(`Ticket status changed to ${latestStatus}.`);
        }

        if (hasNewUpdate && hasNewStatus) {
          const latestStatus = latestTicket.status_history?.at(-1)?.to_status;
          toast.success(
            latestTicket.status === "resolved"
              ? `You have received a new message and ticket is now "${latestStatus}".`
              : `You have received a new message. Ticket status changed to "${latestStatus}".`
          );
        }
      }
    }, 2000);

    return () => clearInterval(pollingRef.current!);
  }, [selected?.id]);

  useEffect(() => {
    const el = chatRef.current;
    if (!el) return;
    const onScroll = () => {
      const isBottom = el.scrollHeight - el.scrollTop - el.clientHeight < 100;
      setShowScrollButton(!isBottom);
      if (isBottom) setNewMsgAlert(false);
    };
    el.addEventListener("scroll", onScroll);
    return () => el.removeEventListener("scroll", onScroll);
  }, []);

  const handleSend = async () => {
    if (!newMessage.trim() && !file) return;
    try {
      setIsSending(true);
      const update = await sendTicketUpdate(selected.id, newMessage, file);
      const [updates, updatedTicket] = await Promise.all([
        getTicketUpdates(selected.id),
        getTicketDetails(selected.id),
      ]);
      const updatedTimeline = buildTimeline(updatedTicket, updates);
      setTimelineMap((prev) => ({ ...prev, [selected.id]: updatedTimeline }));
      lastUpdateIdRef.current[selected.id] = update.id;
      setNewMessage("");
      setFile(null);
      fileInputRef.current!.value = "";
      scrollToBottom();
    } catch {
      toast.error("Failed to send.");
    } finally {
      setIsSending(false);
    }
  };

  const handleStatusChange = async () => {
    try {
      const newStatus = selected.status === "resolved" ? "open" : "resolved";
      setUpdatingStatusId(selected.id);
      const updated = await updateTicketStatus(selected.id, newStatus);
      const newTicket = { ...selected, status: updated.status };
      setTickets((prev) => prev.map((t) => (t.id === selected.id ? newTicket : t)));
      setSelected(newTicket);
    } catch {
      toast.error("Failed to update ticket status");
    } finally {
      setUpdatingStatusId(null);
    }
  };

  const filteredTickets = useMemo(() => {
    return tickets.filter((t) =>
      (t.title?.toLowerCase().includes(search.toLowerCase()) ||
        t.grant_name?.toLowerCase().includes(search.toLowerCase())) &&
      (statusFilter.length === 0 || statusFilter.includes(t.status))
    );
  }, [tickets, search, statusFilter]);

  const getStatusBadgeStyle = (status: string) => {
    switch (status?.toLowerCase()) {
      case "open":
        return "bg-teal-100 text-teal-800 border-teal-50 border-opacity-50";
      case "under review":
        return "bg-amber-100 text-amber-800 border-amber-50 border-opacity-50";
      case "resolved":
        return "bg-teal-100 text-teal-800 border-teal-50 border-opacity-50";
      default:
        return "bg-gray-100 text-gray-700 border-gray-50 border-opacity-50";
    }
  };

  const renderPriorityBadge = (priority: string) => {
    const styles: Record<string, string> = {
      low: "bg-gray-100 text-gray-700 border-gray-50 border-opacity-50",
      medium: "bg-amber-100 text-amber-800 border-amber-50 border-opacity-50",
      high: "bg-orange-100 text-orange-800 border-orange-50 border-opacity-50",
      urgent: "bg-red-100 text-red-800 border-red-50 border-opacity-50",
    };
    return (
      <span
        className={`text-xs font-medium px-2 py-1 rounded-lg ${
          styles[priority?.toLowerCase()] || "bg-gray-100 text-gray-600 border-gray-50 border-opacity-50"
        }`}
      >
        {priority?.replace(/\b\w/g, (l) => l.toUpperCase())}
      </span>
    );
  };

  return (
    <SupportTabsLayout>
      <div className="flex h-[calc(100vh-112px)] bg-white shadow-sm rounded-lg">
<aside className="w-1/3 p-6 space-y-6 bg-gradient-to-br from-gray-50 to-white rounded-l-lg">
  <header className="flex justify-between items-center">
    <Button
      variant="outline"
      size="sm"
      onClick={() => router.push("/grantmaker/support/my-tickets/support-ticket")}
      className="bg-teal-600 hover:bg-teal-500 text-white border-none rounded-lg px-4 py-2 text-sm font-medium transition-all duration-300 ease-in-out hover:shadow-md hover:scale-105"
    >
      ← Back
    </Button>
    <h2 className="text-lg font-semibold text-gray-700 flex items-center gap-2">
      <MessageSquare className="w-5 h-5 text-teal-600 group-hover:scale-110 transition-transform duration-300 ease-in-out" />
      Chats
    </h2>
  </header>

  <div className="flex gap-3">
    <div className="relative flex-grow">
      <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400 group-hover:text-teal-400 transition-colors duration-300" />
      <input
        value={search}
        onChange={(e) => setSearch(e.target.value)}
        placeholder="Search tickets..."
        className="w-full pl-10 pr-2 py-2 border border-gray-100 border-opacity-50 bg-gray-50 rounded-lg text-sm text-gray-700 placeholder:text-gray-400 focus:outline-none focus:border-teal-400 focus:ring-2 focus:ring-teal-100 transition-all duration-300 ease-in-out hover:shadow-md hover:bg-white focus:scale-[1.02]"
      />
    </div>
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="outline"
          size="sm"
          className="bg-teal-600 hover:bg-teal-500 text-white border-none rounded-lg py-2 px-4 transition-all duration-300 ease-in-out hover:shadow-md hover:scale-110"
        >
          <Filter className="w-4 h-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="w-44 bg-white rounded-lg border-gray-100 border-opacity-50 shadow-md">
        <div className="relative flex-grow">
          <DropdownMenuLabel className="text-gray-600 font-medium">Status Filter</DropdownMenuLabel>
          <DropdownMenuSeparator className="bg-gray-100 opacity-50" />
          {STATUSES.map((status) => (
            <DropdownMenuCheckboxItem
              key={status}
              checked={statusFilter.includes(status)}
              onCheckedChange={(checked) =>
                setStatusFilter((prev) =>
                  checked ? [...prev, status] : prev.filter((s) => s !== status)
                )
              }
              className="text-sm text-gray-600 hover:bg-teal-50 hover:text-teal-600 transition-all duration-200 ease-in-out"
            >
              {status.replace("-", " ").replace(/\b\w/g, (l) => l.toUpperCase())}
            </DropdownMenuCheckboxItem>
          ))}
        </div>
      </DropdownMenuContent>
    </DropdownMenu>
  </div>

  <div className="overflow-y-auto space-y-3">
    {filteredTickets.map((t, idx) => (
      <div
        key={t.id}
        className={`cursor-pointer p-4 rounded-lg border border-gray-100 border-opacity-50 transition-all duration-300 ease-in-out ${
          selected?.id === t.id ? "bg-teal-50 shadow-sm" : "hover:bg-gray-100 hover:shadow-sm"
        } flex items-start gap-3`}
        onClick={() => setSelected(t)}
        style={{ animation: `fadeIn 0.4s ease-in-out ${idx * 0.1}s` }}
      >
        <div className="flex-1">

          <p className="text-sm font-medium text-gray-700">{t.title}</p>
          <p className="text-xs text-gray-500">{t.grant_name}</p>
          <p className="text-xs text-gray-400 mt-1">
            Updated: {formatDistanceToNow(new Date(t.updated_at), { addSuffix: true })}
          </p>
            <div className="flex items-center gap-2 mb-1 mt-2">
            {t.priority && renderPriorityBadge(t.priority)}
            <span
              className={`inline-flex items-center px-2 py-1 text-xs font-medium rounded-lg ${getStatusBadgeStyle(t.status)}`}
            >
              {t.status?.replace("-", " ").replace(/\b\w/g, (l) => l.toUpperCase())}
            </span>
          </div>
        </div>
      </div>
    ))}
  </div>
</aside>
      <main className="w-2/3 flex flex-col rounded-r-xl">
        {selected ? (
          <>
            <div className="px-6 py-4 bg-gradient-to-r from-teal-50 to-white flex justify-between items-start flex-wrap border-b border-gray-100 border-opacity-50">
              <div className="border-l-4 border-teal-500 pl-4 flex flex-col sm:flex-row sm:items-center sm:gap-4">
                <div className="flex items-center gap-3 flex-wrap">
                  <p className="text-base font-medium text-gray-800">Ticket #{selected.id}</p>
                  {selected.priority && renderPriorityBadge(selected.priority)}
                  <span
                    className={`inline-flex items-center px-3 py-1 text-xs font-medium rounded-lg ${getStatusBadgeStyle(selected.status)}`}
                  >
                    {selected.status?.replace("-", " ").replace(/\b\w/g, (l) => l.toUpperCase())}
                  </span>
                </div>
                <p className="text-sm text-gray-500">{selected.grant_name}</p>
              </div>
              <div className="flex gap-2">
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() =>
                    router.push(`/grantmaker/support/my-tickets/support-ticket/support-ticket-detail?id=${selected.id}`)
                  }
                  className="border-teal-100 border-opacity-50 text-teal-600 hover:bg-teal-50 rounded-lg px-4 py-2 text-sm font-medium transition-all duration-300 ease-in-out hover:shadow-md hover:scale-105"
                >
                  <Eye className="w-4 h-4 mr-1" /> Detail
                </Button>
                <Button
                  size="sm"
                  variant={selected.status === "resolved" ? "default" : "outline"}
                  onClick={handleStatusChange}
                  disabled={updatingStatusId === selected.id}
                  className={`${
                    selected.status === "resolved"
                      ? "bg-teal-600 hover:bg-teal-500 text-white"
                      : "border-teal-100 border-opacity-50 text-teal-600 hover:bg-teal-50"
                  } rounded-lg px-4 py-2 text-sm font-medium transition-all duration-300 ease-in-out hover:shadow-md hover:scale-105 `}
                >
                  {selected.status === "resolved" ? (
                    <>
                      <Unlock className="w-4 h-4 mr-1 " /> Reopen
                    </>
                  ) : (
                    <>
                      <Lock className="w-4 h-4 mr-1" /> Close
                    </>
                  )}
                </Button>
              </div>
            </div>

            <div
              className="flex-1 overflow-y-auto px-6 py-6 space-y-4 bg-white"
              ref={chatRef}
            >
              {timelineMap[selected?.id || 0]?.length > 0 ? (
                timelineMap[selected.id].map((item, idx) => {
                  if (item.type === "status") {
                    const name = item.user?.first_name
                      ? `${item.user.first_name} ${item.user.last_name}`
                      : item.user?.email;
                    return (
                      <div
                        key={item.id}
                        className="flex justify-center text-sm text-gray-500 my-4"
                        style={{ animation: `fadeIn 0.4s ease-in-out ${idx * 0.05}s` }}
                      >
                        Status changed to{" "}
                        <span
                          className={`inline-flex items-center px-3 py-1 text-xs font-medium rounded-lg mx-1 ${getStatusBadgeStyle(item.to_status)}`}
                        >
                          {item.to_status.replace("-", " ").replace(/\b\w/g, (l) => l.toUpperCase())}
                        </span>{" "}
                        - {format(new Date(item.updated_at), "dd MMM yyyy, HH:mm")}
                      </div>
                    );
                  }

                  const isGM = item.user?.type?.code === "GRANT_MAKER";
                  const name = item.user?.first_name
                    ? `${item.user.first_name} ${item.user.last_name}`
                    : item.user?.email;
                  return (
                    <div
                      key={item.id}
                      className={`flex ${isGM ? "justify-end" : "justify-start"} transition-all duration-300 ease-in-out hover:bg-teal-50 rounded-lg p-2`}
                      style={{ animation: `fadeIn 0.4s ease-in-out ${idx * 0.05}s` }}
                    >
                      <div
                        className={`rounded-lg px-4 py-3 text-sm max-w-md shadow-sm border border-opacity-50 ${
                          isGM
                            ? "bg-teal-100 text-teal-800 border-teal-50"
                            : "bg-gray-100 text-gray-700 border-gray-50"
                        } transition-all duration-300 ease-in-out hover:shadow-md`}
                      >
                        <p className="text-xs font-medium text-gray-600 mb-1">
                          {name} · {formatDistanceToNow(new Date(item.updated_at), { addSuffix: true })}
                        </p>
                        <p className="whitespace-pre-line">{item.update_text}</p>
                        {item.attachments?.map((url: string, i: number) => (
                          <a
                            key={i}
                            href={url}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="block text-xs text-teal-600 underline mt-2 hover:text-teal-800 transition-colors duration-200"
                          >
                            <Paperclip className="inline w-4 h-4 mr-1" />
                            {new URL(url).pathname.split("/").pop()?.split("?")[0]}
                          </a>
                        ))}
                      </div>
                    </div>
                  );
                })
              ) : (
                <div
                  className="p-8 text-center text-gray-600"
                  style={{ animation: `fadeIn 0.4s ease-in-out` }}
                >
                  <AlertCircle className="w-8 h-8 mb-3 mx-auto text-gray-400" />
                  <p className="text-sm font-medium text-gray-700">No messages available.</p>
                </div>
              )}
              {newMsgAlert && showScrollButton && (
                <div className="fixed bottom-28 right-6 animate-pulse">
                  <Button
                    onClick={scrollToBottom}
                    size="sm"
                    className="bg-teal-600 hover:bg-teal-500 text-white rounded-full px-4 py-2 text-xs font-medium shadow-md transition-all duration-300 ease-in-out hover:scale-110"
                  >
                    New messages <ArrowDown className="w-4 h-4 ml-1" />
                  </Button>
                </div>
              )}
            </div>

            <footer className="p-6 bg-white flex items-center gap-4 border-t border-gray-100 border-opacity-50">
              {selected.status === "resolved" ? (
                <p className="text-sm text-gray-600">This ticket is closed.</p>
              ) : (
                <>
                  <button
                    onClick={() => fileInputRef.current?.click()}
                    className="group"
                    title="Attach file"
                  >
                    <Paperclip className="w-6 h-6 text-gray-400 group-hover:text-teal-400 transition-all duration-300 ease-in-out hover:scale-110" />
                  </button>
                  <input
                    ref={fileInputRef}
                    type="file"
                    onChange={(e) => setFile(e.target.files?.[0] || null)}
                    className="hidden"
                    id="file-input"
                  />
                  {file && (
                    <span className="text-xs text-gray-600 truncate max-w-[160px] bg-gray-50 px-3 py-1.5 rounded-lg border border-gray-100 border-opacity-50">
                      {file.name}
                    </span>
                  )}
                  <textarea
                    value={newMessage}
                    onChange={(e) => setNewMessage(e.target.value)}
                    placeholder="Type your message..."
                    className="flex-1 border border-gray-100 border-opacity-50 rounded-lg px-4 py-3 text-sm text-gray-700 placeholder:text-gray-400 bg-gray-50 focus:outline-none focus:border-teal-200 focus:ring-2 focus:ring-teal-100 transition-all duration-300 ease-in-out min-h-[80px] max-h-[200px] resize-y hover:shadow-sm focus:shadow-md focus:bg-white focus:scale-[1.02]"
                  />
                  <Button
                    disabled={isSending || (!newMessage.trim() && !file)}
                    onClick={handleSend}
                    className="bg-teal-600 hover:bg-teal-500 text-white px-6 py-3 text-sm font-medium rounded-lg transition-all duration-300 ease-in-out hover:shadow-md hover:scale-105 disabled:bg-gray-300 disabled:text-gray-500 disabled:scale-100"
                  >
                    {isSending ? "Sending..." : "Send"}
                  </Button>
                </>
              )}
            </footer>
          </>
        ) : (
          <div
            className="flex flex-1 items-center justify-center flex-col gap-4 text-center text-gray-600 p-8"
            style={{ animation: `fadeIn 0.4s ease-in-out` }}
          >
            <MessageSquare className="w-12 h-12 text-gray-300" />
            <p className="text-xl font-medium text-gray-700">No tickets selected</p>
            <p className="text-sm text-gray-500 max-w-sm">
              Select a ticket from the list to view or respond to messages.
            </p>
          </div>
        )}
      </main>
    </div>
  </SupportTabsLayout>
);
}