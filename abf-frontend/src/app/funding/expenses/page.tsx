'use client';

import { useRouter } from 'next/navigation';
import { useState, useEffect, useMemo, Fragment, act, use } from 'react';
import { Upload, ChevronDown, ChevronUp, X, CheckCircle, History, Edit, Eye, FileSpreadsheet } from 'lucide-react';
import { Button } from '@/components/ui/button';
import * as XLSX from 'xlsx';
import { debounce } from 'lodash';
import { getGrants, createExpense, fetchExpensesByGrant, bulkUpdateExpenses } from '@/services/expenses-service';
import { Select, SelectValue, SelectTrigger, SelectItem, SelectContent } from '@/components/ui/select';
import { FileText } from 'lucide-react';
import { GrantAPIResponse } from '@/types/profile';
import { AxiosError } from 'axios';
import { EditHistoryLog } from '@/types/expenses';

import ExpensesHistoryTable from '@/components/funding/expense/ExpensesHistoryTable';
import ExpenseTable from '@/components/funding/expense/ExpenseTable';
import ErrorPopup from '@/components/funding/expense/ErrorPopup';
import SuccessPopup from '@/components/funding/expense/SuccessPopup';
import ExcelUploadModal from '@/components/funding/expense/ExcelUploadModal';
import { useExpenseData } from '@/contexts/ExpenseDataContext';




interface ExpenseRow {
  id: number;
  particulars: string;
  main_header: string;
  sub_header: string;
  units: string;
  frequency: string;
  cost_per_unit: string;
  activity_description: string;
  budget_quarterly: Record<string, number>;
  actual_quarterly: Record<string, number>;
  total_budget: number;
  total_grant_budget: number;
  total_actual: number;
  grant: string;
  remarks: string;

}

// Utility functions
const debounceValidation = debounce((fn: () => void) => fn(), 100);

const isDuplicateLog = (
  existingLogs: EditHistoryLog[],
  rowId: number,
  field: string,
  newValue: string,
  timestamp: string
): boolean => {
  const recentLog = existingLogs
    .filter((log) => log.row_id === rowId && log.field === field)
    .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())[0];
  if (!recentLog) return false;
  const timeDiff = Math.abs(new Date(timestamp).getTime() - new Date(recentLog.timestamp).getTime());
  return recentLog.new_value === newValue && timeDiff < 1000;
};
export default function ExpensesPage() {
  const router = useRouter();
  const { setExcelData: setContextExcelData } = useExpenseData();
  const [file1, setFile1] = useState<File | null>(null);
  const [file2, setFile2] = useState<File | null>(null);
  const [file3, setFile3] = useState<File | null>(null);
  const [file4, setFile4] = useState<File | null>(null);

  const [expandedRows, setExpandedRows] = useState<number[]>([]);
  const [budgetValues, setBudgetValues] = useState<Record<string, number>>({});
  const [actualValues, setActualValues] = useState<Record<string, number>>({});
  const [remarksValues, setRemarksValues] = useState<Record<number, string>>({});
  const [remarksErrors, setRemarksErrors] = useState<Record<number, string>>({});
  const [isUploadModalOpen, setIsUploadModalOpen] = useState(false);
  const [excelData, setExcelData] = useState<ExpenseRow[]>([]);
  const [errorPopup, setErrorPopup] = useState<string | null>(null);
  const [successPopup, setSuccessPopup] = useState<string | null>(null);
  const [rowErrors, setRowErrors] = useState<Record<number, Record<string, string>>>({});
  const [totalGrantBudgets, setTotalGrantBudgets] = useState<Record<number, number>>({});
  const [budgetSumErrors, setBudgetSumErrors] = useState<Record<number, Record<string, string>>>({});
  const [initialBudgetSums, setInitialBudgetSums] = useState<Record<number, number>>({});
  const [grants, setGrants] = useState<{ id: string; grant_name: string }[]>([]);
  const [selectedGrant, setSelectedGrant] = useState('');
  const [editHistory, setEditHistory] = useState<EditHistoryLog[]>([]);
  const [isHistoryModalOpen, setIsHistoryModalOpen] = useState(false);
  const [historyRowId, setHistoryRowId] = useState<number | null>(null);
  const [isClient, setIsClient] = useState(false);
  const [isLoadingGrants, setIsLoadingGrants] = useState(true);
  const [grantError, setGrantError] = useState<string | null>(null);
  const [expenses, setExpenses] = useState<ExpenseRow[]>([]);
  const [doesSelectedGrantHaveExpenses, setDoesSelectedGrantHaveExpenses] = useState(false);
  const [submissionErrors, setSubmissionErrors] = useState<Record<number, Record<string, string>>>({});
  const [shouldUpdateExpenses, setShouldUpdateExpenses] = useState(Date.now().toString());
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Financial quarters for India
  const quarters = ['Apr-Jun', 'Jul-Sep', 'Oct-Dec', 'Jan-Mar'];

  // Determine current financial quarter
  const today = new Date();
  const month = today.getMonth(); // 0 = Jan, 11 = Dec
  let currentQuarter = 1;
  if (month >= 3 && month <= 5) currentQuarter = 1; // Apr-Jun
  else if (month >= 6 && month <= 8) currentQuarter = 2; // Jul-Sep
  else if (month >= 9 && month <= 11) currentQuarter = 3; // Oct-Dec
  else currentQuarter = 4; // Jan-Mar

  // Fetch expenses when selectedGrant changes
  useEffect(() => {
    const fetchExpenses = async () => {
      try {
        const response = await fetchExpensesByGrant(selectedGrant);
        if (response.status === 'SUCCESS') {
          const expenses = response.data;
          const mappedExpenses: ExpenseRow[] = expenses.map((expense: any) => ({
            id: expense.id,
            particulars: expense.particulars ?? '',
            main_header: expense.main_header ?? '',
            sub_header: expense.sub_header ?? '',
            units: String(expense.units ?? '0'),
            frequency: expense.frequency ?? '',
            cost_per_unit: String(expense.cost_per_unit ?? '0'),
            activity_description: expense.activity_description ?? '',
            budget_q1: Number(expense.budget_q1) || 0,
            budget_q2: Number(expense.budget_q2) || 0,
            budget_q3: Number(expense.budget_q3) || 0,
            budget_q4: Number(expense.budget_q4) || 0,
            actual_q1: Number(expense.actual_q1) || 0,
            actual_q2: Number(expense.actual_q2) || 0,
            actual_q3: Number(expense.actual_q3) || 0,
            actual_q4: Number(expense.actual_q4) || 0,
            grant: String(expense.grant ?? ''),
            remarks: expense.remarks ?? '',
            status: expense.status ?? 'pending',
            is_frozen: !!expense.is_frozen,
            metadata: expense.metadata ?? { edit_history: [] },
          }));

          if (expenses.length > 0) {
            setDoesSelectedGrantHaveExpenses(true);
          } else {
            setDoesSelectedGrantHaveExpenses(false);
          }


          const newBudgetValues: Record<string, number> = {};
          const newActualValues: Record<string, number> = {};
          const newRemarksValues: Record<number, string> = {};
          const newTotalGrantBudgets: Record<number, number> = {};
          const newInitialBudgetSums: Record<number, number> = {};
          const newEditHistory: EditHistoryLog[] = [];

          mappedExpenses.forEach((row) => {
            quarters.forEach((q, idx) => {
              newBudgetValues[`${row.id}-${q}`] = row[`budget_q${idx + 1}` as keyof ExpenseRow];
              newActualValues[`${row.id}-${q}`] = row[`actual_q${idx + 1}` as keyof ExpenseRow];
            });
            newRemarksValues[row.id] = row.remarks;
            newTotalGrantBudgets[row.id] = Number(row.units || 1) * Number(row.cost_per_unit || 1) * Number(row.frequency || 1) || 0;
            newInitialBudgetSums[row.id] = totalGrantBudgets[row.id]
            if (row.metadata.edit_history) {
              newEditHistory.push(...row.metadata.edit_history.map((log: any) => ({ ...log, row_id: row.id })));
            }
          });

          setExpenses(mappedExpenses);
          setBudgetValues(newBudgetValues);
          setActualValues(newActualValues);
          setRemarksValues(newRemarksValues);
          setTotalGrantBudgets(newTotalGrantBudgets);
          setInitialBudgetSums(newInitialBudgetSums);
          setExcelData(expenses);
        }
      } catch (error) {
        const err = error as AxiosError<{ message: string }>;
        if (err.response?.status) {
          console.log("Error fetching expenses:", err.response.status);
        }
        console.log("Error fetching expenses:", error);
      }
    }
    fetchExpenses();

  }, [selectedGrant])

  useEffect(() => {
    setIsClient(true);
  }, []);

  // Load edit history from localStorage
  useEffect(() => {
    if (isClient) {
      const saved = localStorage.getItem('editHistory');
      if (saved) {
        setEditHistory(JSON.parse(saved));
      }
    }
  }, [isClient]);

  // Save edit history to localStorage
  useEffect(() => {
    if (isClient) {
      localStorage.setItem('editHistory', JSON.stringify(editHistory));
    }
  }, [editHistory, isClient]);

  // Fetch grants
  useEffect(() => {
    const fetchGrants = async () => {
      try {
        setIsLoadingGrants(true);
        const grantsData = await getGrants();
        if (!Array.isArray(grantsData) || grantsData.length === 0) {
          // throw new Error('No grants available or invalid data received.');
        }
        const normalizedGrants = grantsData.map((grant: GrantAPIResponse) => ({
          id: String(grant.id),
          grant_name: grant.grant_name,
        }));
        setGrants(normalizedGrants);

        if (normalizedGrants.length > 0) {
          setSelectedGrant(normalizedGrants[0].id);
        }
      } catch (error) {
        console.error('Error fetching grants:', error);
        setGrantError('Failed to load grants: ' + (error as Error).message);
        setErrorPopup('Failed to load grants. Please try again or contact support.');
      } finally {
        setIsLoadingGrants(false);
      }
    };
    fetchGrants();
  }, []);

  // Update context when excelData changes
  useEffect(() => {
    if (excelData.length > 0) {
      console.log('Updating context with excel data:', excelData.length, 'records');
      setContextExcelData(excelData);
    }
  }, [excelData, setContextExcelData]);

  // Real-time validation on input changes
  useEffect(() => {
    if (excelData.length > 0) {
      debounceValidation(runAllValidations);
    }
  }, [budgetValues, actualValues, remarksValues, excelData]);

  // Compute updated excelData
  const updatedExcelData = useMemo(() => {
    return excelData.map((row) => {
      const rowId = row.id;
      const budgetQuarterly = { ...row.budget_quarterly };
      const actualQuarterly = { ...row.actual_quarterly };

      quarters.forEach((q) => {
        budgetQuarterly[q] =
          parseFloat(String(budgetValues[`${rowId}-${q}`] ?? '0'));
        actualQuarterly[q] =
          parseFloat(String(actualValues[`${rowId}-${q}`] ?? '0'));
      });

      const totalActual = quarters.reduce((sum, q) => sum + actualQuarterly[q], 0);
      const remarks = remarksValues[rowId] || row.remarks || '';

      return {
        ...row,
        budget_quarterly: budgetQuarterly,
        actual_quarterly: actualQuarterly,
        total_actual: totalActual,
        remarks,
      };
    });
  }, [excelData, budgetValues, actualValues, remarksValues]);

  // Update context when user makes changes to the data
  useEffect(() => {
    if (updatedExcelData.length > 0) {
      console.log('Updating context with user-modified excel data:', updatedExcelData.length, 'records');
      setContextExcelData(updatedExcelData);
    }
  }, [updatedExcelData, setContextExcelData]);

  // Validation functions
  const validateBudgetSums = () => {
    const newBudgetSumErrors: Record<number, Record<string, string>> = {};
    updatedExcelData.forEach((row) => {
      const rowId = row.id;
      const totalBudget = quarters.reduce(
        (acc, q) => acc + (parseFloat(String(budgetValues[`${rowId}-${q}`] ?? '0'))),
        0
      );


      const roundedTotalBudget = parseFloat(totalBudget.toFixed(2));
      const units = parseFloat(row['units']) || 0;
      const costPerUnit = parseFloat(row['cost_per_unit']) || 0;
      const frequency = parseFloat(row['frequency']) || 1; // Default to 1 if frequency is not set
      const totalGrantBudget = units * costPerUnit * frequency;
      setTotalGrantBudgets((prev) => ({
        ...prev,
        [rowId]: totalGrantBudget
      }));

      const errors: Record<string, string> = {};
      if (roundedTotalBudget !== totalGrantBudget) {
        errors.initialSumError = `Total Budget (${roundedTotalBudget.toLocaleString('en-IN', {
          style: 'currency',
          currency: 'INR',
          minimumFractionDigits: 2,
          maximumFractionDigits: 2,
        })}) does not matches the total grants budget of (${totalGrantBudgets[rowId].toLocaleString('en-IN', {
          style: 'currency',
          currency: 'INR',
          minimumFractionDigits: 2,
          maximumFractionDigits: 2,
        })}).`;
      }

      if (Object.keys(errors).length > 0) {
        newBudgetSumErrors[rowId] = errors;
      }
    });

    setBudgetSumErrors((prev) => {
      const updatedErrors = { ...prev };
      Object.keys(updatedErrors).forEach((rowId) => {
        if (!newBudgetSumErrors[parseInt(rowId)]) {
          delete updatedErrors[parseInt(rowId)];
        }
      });
      return { ...updatedErrors, ...newBudgetSumErrors };
    });
  };

  const validateRemarks = () => {
    const newRemarksErrors: Record<number, string> = {};
    updatedExcelData.forEach((row) => {
      const rowId = row.id;
      const totalGrantBudget = totalGrantBudgets[rowId] || 0;
      const totalActual = quarters.reduce(
        (sum, q) => sum + (parseFloat(String(actualValues[`${rowId}-${q}`] ?? '0'))),
        0
      );

      const remark = remarksValues[rowId] ?? ''

      // At the top of the loop for each row, insert:
      let requiresRemark = false;

      // If total actual > total grant budget, require remark
      if (totalActual > totalGrantBudget) {
        requiresRemark = true;
      }

      // For each quarter, check overspent/underspent and update requiresRemark
      let isOverspent = false;
      let isUnderspent = false;
      quarters.forEach((q) => {
        const actual = parseFloat(String(actualValues[`${rowId}-${q}`] ?? '0'));
        const budget = parseFloat(String(budgetValues[`${rowId}-${q}`] ?? '0'));
        const quarterIndex = quarters.indexOf(q) + 1;
        if (quarterIndex < currentQuarter) {
          return; // Skip if previous or current quarter
        } else if (quarterIndex > currentQuarter) {
          if (actual > 0) {
            //requiresRemark = true; // Require remark if actual is entered for future quarters
          }
        } else {
          // Current quarter
          isOverspent = actual > budget * 1.1;
          isUnderspent = actual < budget / 2;
          if (isOverspent || isUnderspent) {
            requiresRemark = true;
          }
        }
      });

      // After both checks, assign error only if requiresRemark and remark is empty
      if (requiresRemark && !remark.trim() && isUnderspent) {
        newRemarksErrors[rowId] = 'Your budget utilization is currently below 50% for this quarter. We understand that unplanned needs or changes can come up - please use the space below to briefly share what led to this, so we can stay aligned.';
      } else if (requiresRemark && !remark.trim() && isOverspent) {
        newRemarksErrors[rowId] = "Your spending has exceeded the budget by more than 10%. Please briefly explain what led to this overspend.";

      }
      else {
        delete newRemarksErrors[rowId];
      }
    });

    setRemarksErrors((prev) => {
      const updatedErrors = { ...prev };
      Object.keys(updatedErrors).forEach((rowId) => {
        if (!newRemarksErrors[parseInt(rowId)]) {
          delete updatedErrors[parseInt(rowId)];
        }
      });
      return { ...updatedErrors, ...newRemarksErrors };
    });

    setRowErrors((prev) => {
      const updated = { ...prev };

      Object.keys(updated).forEach((rowId) => {
        const numericRowId = parseInt(rowId);

        // Skip this row if a remark is still missing — we don't clean anything
        if (!newRemarksErrors[numericRowId]) {
          const filtered = Object.fromEntries(
            Object.entries(updated[numericRowId]).filter(([key, message]) => {
              // Keep errors if:
              // - They are not actual-* or remarks
              // - Or they are actual-* but NOT related to overspent/underspent
              const isActual = key.startsWith('actual-');
              const isOverspent = typeof message === 'string' && message.toLowerCase().includes('overspent');
              const isUnderspent = typeof message === 'string' && message.toLowerCase().includes('underspent');

              // Remove overspent/underspent only if remark is now present
              return !isActual || (!isOverspent && !isUnderspent);
            })
          );

          if (Object.keys(filtered).length === 0) {
            delete updated[numericRowId];
          } else {
            updated[numericRowId] = filtered;
          }
        }
      });

      return updated;
    });

  };

  const validateRowData = (row: ExpenseRow, index: number) => {
    const errors: Record<string, string> = {};
    const rowId = row.id || index + 1;

    // Validate particulars
    if (!row.particulars || row.particulars.trim() === '') {
      errors.particulars = 'Particulars is required and cannot be empty.';
    }

    // Validate units
    const units = parseFloat(row.units);
    if (isNaN(units) || units <= 0) {
      errors.units = 'Units must be a positive number.';
    }

    // Validate cost_per_unit
    const costPerUnit = parseFloat(row.cost_per_unit);
    if (isNaN(costPerUnit) || costPerUnit < 0) {
      errors.cost_per_unit = 'Cost per unit cannot be negative.';
    }

    // TODO: See if this can be removed as we are not even able to enter negative values
    quarters.forEach((quarter) => {
      const budgetValue = parseFloat(String(budgetValues[`${rowId}-${quarter}`] ?? '0'));
      if (budgetValue < 0) {
        errors[`budget-${quarter}`] = `${quarter} budget cannot be negative.`;
      }
    });

    quarters.forEach((quarter) => {
      const key = `${rowId}-${quarter}`;
      const actual = parseFloat(String(actualValues[key] ?? '0'));
      const budget = budgetValues[key] || 0;

      const quarterIndex = quarters.indexOf(quarter) + 1;
      const isFutureQuarter = quarterIndex > currentQuarter;
      const isCurrentQuarter = quarterIndex === currentQuarter;

      if (isFutureQuarter && actual > 0) {
        errors[`actual-${quarter}`] = 'Actual cannot be entered for future quarters.';
        return;
      }

      // Skip validation if actual is 0 and either:
      // - It's not the current quarter, or
      // - Budget is also 0
      if (actual === 0 && (!isCurrentQuarter || budget === 0)) return;

      if (actual < 0) {
        errors[`actual-${quarter}`] = `${quarter} actual cannot be negative.`;
      } else if (actual > budget * 1.1) {
        errors[`actual-${quarter}`] = `${quarter} overspent detected - exceeds budget by more than 10%.`;
      } else if (actual < budget / 2) {
        errors[`actual-${quarter}`] = `${quarter} underspent detected - less than 50% of budget utilized.`;
      }
    });

    return errors;
  };
  const validateAllRows = () => {
    const newRowErrors: Record<number, Record<string, string>> = {};

    updatedExcelData.forEach((row, index) => {
      const rowValidationErrors = validateRowData(row, index);
      if (Object.keys(rowValidationErrors).length > 0) {
        newRowErrors[row.id] = rowValidationErrors;
      }
    });

    setRowErrors((prev) => {
      const updatedErrors = { ...prev };

      Object.keys(updatedErrors).forEach((rowId) => {
        if (!newRowErrors[parseInt(rowId)]) {
          delete updatedErrors[parseInt(rowId)];
        }
      });

      const merged = { ...updatedErrors, ...newRowErrors };
      return merged;
    });
  };


  const runAllValidations = () => {
    validateBudgetSums();
    validateAllRows();
    validateRemarks();
    // Rows remain collapsed by default - users can manually expand to see validation errors
    console.log("Validation completed - rows remain collapsed for manual expansion");
  };

  const logEdit = (rowId: number, field: string, oldValue: string, newValue: string) => {
    const timestamp = new Date().toISOString();
    setEditHistory((prev) => {
      if (isDuplicateLog(prev, rowId, field, newValue, timestamp)) {
        return prev;
      }
      const newLog: EditHistoryLog = {
        row_id: rowId,
        field,
        old_value: oldValue,
        new_value: newValue,
        timestamp,
      };
      return [...prev, newLog];
    });
  };

  const isBudgetEditable = (quarter: string) => {
    const quarterIndex = quarters.indexOf(quarter) + 1;
    if (currentQuarter === 3 && quarterIndex === 4) return false;
    return quarterIndex > currentQuarter;
  };

  const isActualEditable = (quarter: string) => {
    const quarterIndex = quarters.indexOf(quarter) + 1;
    return quarterIndex === currentQuarter;
  };

  const validateNumber = (value: number, fieldName: string) => {
    if (isNaN(value) || value < 0) {
      return { isValid: false, message: `${fieldName} cannot be negative.` };
    }
    return { isValid: true, message: '' };
  };


  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) {
      setErrorPopup('No file selected.');
      return;
    }

    if (!file.name.endsWith('.xlsx') && !file.name.endsWith('.xls')) {
      setErrorPopup('Please upload a valid Excel file (.xlsx or .xls).');
      return;
    }

    if (isLoadingGrants) {
      setErrorPopup('Grants are still loading. Please wait.');
      return;
    }

    if (!selectedGrant) {
      setErrorPopup('Please select a grant before uploading.');
      return;
    }

    const selectedGrantObj = grants.find((g) => g.id === selectedGrant);
    if (!selectedGrantObj) {
      console.error('Invalid grant selected:', { selectedGrant, grants });
      setErrorPopup(`Invalid grant selected (ID: ${selectedGrant}). Please choose a valid grant.`);
      return;
    }

    setEditHistory([]);
    setSubmissionErrors({});
    if (isClient) {
      localStorage.setItem('editHistory', JSON.stringify([]));
    }

    const readerForStorage = new FileReader();
    readerForStorage.onload = (e) => {
      if (isClient) {
        const fileData = e.target?.result as string;
        localStorage.setItem('uploadedExcelFile', fileData);
      }
    };
    readerForStorage.readAsDataURL(file);

    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const data = new Uint8Array(e.target?.result as ArrayBuffer);
        const workbook = XLSX.read(data, { type: 'array' });
        const sheetName = workbook.SheetNames[0];
        if (!sheetName) {
          setErrorPopup('Excel file is empty.');
          return;
        }

        const worksheet = workbook.Sheets[sheetName];
        const jsonData = XLSX.utils.sheet_to_json<Record<string, any>>(worksheet, { defval: '' });

        if (jsonData.length === 0) {
          setErrorPopup('Excel file contains no data.');
          return;
        }

        const emptyRows = jsonData.filter((row: any) =>
          Object.values(row).every((value) => !value || value.toString().trim() === '')
        );
        if (emptyRows.length > 0) {
          setErrorPopup('Excel file contains empty rows, which is not allowed.');
          return;
        }

        const headers = Object.keys(jsonData[0]);
        const requiredColumns = ['Particulars', 'No. of Units', 'Cost per Unit'];
        const missingColumns = requiredColumns.filter((col) => !headers.includes(col));
        if (missingColumns.length > 0) {
          setErrorPopup(`Missing required columns: ${missingColumns.join(', ')}.`);
          return;
        }
        // Normalize headers: convert "No. of Units" to "Units"
        jsonData.forEach((row) => {
          if ('No. of Units' in row && !('Units' in row)) {
            row['Units'] = row['No. of Units'];
          }
        });


        const uniqueData = jsonData.filter((row: any, index: number, self: any[]) => {
          const rowString = `${row['Particulars']}-${row['Units']}-${row['Cost per Unit']}`;
          const firstIndex = self.findIndex(
            (r) => `${r['Particulars']}-${r['Units']}-${r['Cost per Unit']}` === rowString
          );
          return index === firstIndex;
        });

        const newRowErrors: Record<number, Record<string, string>> = {};
        const newTotalGrantBudgets: Record<number, number> = {};
        const newInitialBudgetSums: Record<number, number> = {};
        const newBudgetValues: Record<string, number> = {};
        const newActualValues: Record<string, number> = {};
        const newRemarksValues: Record<number, string> = {};
        const newRemarksErrors: Record<number, string> = {};

        const mappedData: ExpenseRow[] = uniqueData.map((row: any, index: number) => {
          const rowId = index + 1;

          const units = parseFloat(row['Units']) || 1;
          const costPerUnit = parseFloat(row['Cost per Unit']) || 1;
          const frequency = parseFloat(row['Frequency']) || 1; // Default to 1 if frequency is not set
          const totalGrantBudget = units * costPerUnit * frequency;

          const budgetQuarterly: Record<string, number> = {};
          const actualQuarterly: Record<string, number> = {};

          quarters.forEach((q, idx) => {
            const qNum = idx + 1;
            const budgetKey = `Budget Quarterly Breakup - Q${qNum}`;
            const actualKey = `Actual Quarterly Breakup - Q${qNum}`;
            const budgetValue = parseFloat(row[budgetKey]) || 0;
            const actualValue = parseFloat(row[actualKey]) || 0;
            budgetQuarterly[q] = budgetValue;
            actualQuarterly[q] = actualValue;
            newBudgetValues[`${rowId}-${q}`] = budgetValue;
            newActualValues[`${rowId}-${q}`] = actualValue;
          });

          const initialBudgetSum = quarters.reduce((sum, q) => sum + budgetQuarterly[q], 0);
          const totalActual = quarters.reduce((sum, q) => sum + actualQuarterly[q], 0);
          const remarks = (row['Remarks'] || '').toString().trim();

          const mappedRow: ExpenseRow = {
            id: rowId,
            particulars: (row['Particulars'] || '').toString().trim(),
            main_header: (row['Main Header'] || '').toString().trim(),
            sub_header: (row['Sub Header'] || '').toString().trim(),
            units: (row['Units'] || '').toString().trim(),
            frequency: (row['Frequency'] || '').toString().trim(),
            cost_per_unit: (row['Cost per Unit'] || '').toString().trim(),
            activity_description: (row['Activity Description'] || '').toString().trim(),
            budget_quarterly: budgetQuarterly,
            actual_quarterly: actualQuarterly,
            total_budget: initialBudgetSum,
            total_grant_budget: totalGrantBudget,
            total_actual: totalActual,
            grant: selectedGrant,
            remarks,
          };

          const rowValidationErrors = validateRowData(mappedRow, index);
          if (Object.keys(rowValidationErrors).length > 0) {
            newRowErrors[rowId] = rowValidationErrors;
          }

          newTotalGrantBudgets[rowId] = totalGrantBudget;
          newInitialBudgetSums[rowId] = initialBudgetSum;
          newRemarksValues[rowId] = remarks;

          if (totalGrantBudget > 0 && totalActual > totalGrantBudget && !remarks.trim()) {
            newRemarksErrors[rowId] = 'Remarks are required when actual expenses exceed the total grant budget.';
          }

          return mappedRow;
        });

        setExcelData(mappedData);
        setRowErrors(newRowErrors);
        setTotalGrantBudgets(newTotalGrantBudgets);
        setInitialBudgetSums(newInitialBudgetSums);
        setBudgetValues(newBudgetValues);
        setActualValues(newActualValues);
        setRemarksValues(newRemarksValues);
        setRemarksErrors(newRemarksErrors);

        const initialBudgetSumErrors: Record<number, Record<string, string>> = {};
        mappedData.forEach((row) => {
          const rowId = row.id;
          const totalBudget = quarters.reduce(
            (acc, q) => acc + (parseFloat(String(newBudgetValues[`${rowId}-${q}`] ?? '0'))),
            0
          );
          const roundedTotalBudget = parseFloat(totalBudget.toFixed(2));
          const initialBudgetSum = newInitialBudgetSums[rowId] || 0;

          const errors: Record<string, string> = {};
          if (initialBudgetSum > 0 && roundedTotalBudget != totalGrantBudgets[rowId]) {
            const diff = roundedTotalBudget - initialBudgetSum;
            errors.initialSumError = `Total Budget (${roundedTotalBudget.toLocaleString('en-IN', {
              style: 'currency',
              currency: 'INR',
              minimumFractionDigits: 2,
              maximumFractionDigits: 2,
            })}) does not matches the total grants budget(${initialBudgetSum.toLocaleString('en-IN', {
              style: 'currency',
              currency: 'INR',
              minimumFractionDigits: 2,
              maximumFractionDigits: 2,
            })})`;
          }

          if (Object.keys(errors).length > 0) {
            initialBudgetSumErrors[rowId] = errors;
          }
        });

        setBudgetSumErrors(initialBudgetSumErrors);

        // Rows remain collapsed by default - users can manually expand to see errors
      } catch (error) {
        setErrorPopup('Error processing Excel file: ' + (error as Error).message);
      }
    };
    reader.readAsArrayBuffer(file);
    setIsUploadModalOpen(false);
  };

  const toggleRow = (rowId: number) => {
    setExpandedRows((prev) =>
      prev.includes(rowId) ? prev.filter((r) => r !== rowId) : [...prev, rowId]
    );
  };

  const calculateTotal = (rowId: number, type: 'budget' | 'actual') => {
    const values = quarters.map((q) => {
      const value = type === 'actual' ? actualValues[`${rowId}-${q}`] : budgetValues[`${rowId}-${q}`];
      return parseFloat(value as any) || 0;
    });
    const total = values.reduce((sum, val) => sum + val, 0);
    return total.toLocaleString('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    });
  };

  const getInitialBudgetSum = (rowId: number) => {
    const initialBudgetSum = initialBudgetSums[rowId] || 0;
    return initialBudgetSum.toLocaleString('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    });
  };

  const handleBudgetChange = (rowId: number, quarter: string, val: number) => {
    setBudgetValues((prev) => {
      const oldValue = prev[`${rowId}-${quarter}`] || 0;
      const newValue = val;
      if (oldValue !== newValue) {
        logEdit(rowId, `Budget ${quarter}`, oldValue.toString(), newValue.toString());
      }
      return { ...prev, [`${rowId}-${quarter}`]: newValue };
    });
    debounceValidation(runAllValidations);
  };

  const handleActualChange = (rowId: number, quarter: string, val: number) => {
    setActualValues((prev) => {
      const oldValue = prev[`${rowId}-${quarter}`] || 0;
      const newValue = val;
      if (oldValue !== newValue) {
        logEdit(rowId, `Actual ${quarter}`, oldValue.toString(), newValue.toString());
      }
      return { ...prev, [`${rowId}-${quarter}`]: newValue };
    });
    debounceValidation(runAllValidations);
  };

  const handleRemarksChange = (rowId: number, val: string) => {
    setRemarksValues((prev) => {
      const oldValue = prev[rowId] || '';
      const newValue = val;
      if (oldValue !== newValue) {
        logEdit(rowId, 'Remarks', oldValue, newValue);
      }
      return { ...prev, [rowId]: newValue };
    });
    debounceValidation(runAllValidations);
  };

  const handleSubmit = async () => {

    if (isSubmitting) {
      setErrorPopup('Submission is in progress. Please wait.');
      return;
    }

    setIsSubmitting(true);

    if (excelData.length === 0) {
      setErrorPopup('No expenses to submit. Please upload an Excel file.');
      setIsSubmitting(false);
      return;
    }

    // Run validations and wait for completion
    await new Promise((resolve) => {
      debounceValidation(() => {
        runAllValidations();
        resolve(null);
      });
    });

    // Validate data before submission
    const newSubmissionErrors: Record<number, Record<string, string>> = {};
    updatedExcelData.forEach((row) => {
      const rowId = row.id;
      const errors: Record<string, string> = {};

      // Validate particulars
      if (!row.particulars.trim()) {
        errors.particulars = 'Particulars is required and cannot be empty.';
      }

      // Validate units
      const units = parseFloat(row.units);
      if (isNaN(units) || units <= 0) {
        errors.units = 'Units must be a positive number.';
      }

      // Validate cost_per_unit
      const costPerUnit = parseFloat(row.cost_per_unit);
      if (isNaN(costPerUnit) || costPerUnit <= 0) {
        errors.cost_per_unit = 'Cost per unit cannot be negative.';
      }
      const frequency = parseFloat(row.frequency);
      if (isNaN(frequency) || frequency <= 0) {
        errors.frequency = 'Frequency must be a positive number.';
      }

      // Validate total budget
      const totalBudget = quarters.reduce(
        (sum, q) => sum + (parseFloat(String(budgetValues[`${rowId}-${q}`] ?? '0'))),
        0
      );
      if (totalBudget <= 0) {
        errors.budget = 'At least one quarterly budget must be non-zero.';
      }

      // Validate remarks
      const totalGrantBudget = parseFloat(row.units) * parseFloat(row.cost_per_unit);
      const totalActual = quarters.reduce(
        (sum, q) => sum + (parseFloat(String(actualValues[`${rowId}-${q}`] ?? '0'))),
        0
      );
      if (totalGrantBudget > 0 && totalActual > totalGrantBudget && !row.remarks.trim()) {
        errors.remarks = 'Remarks are required when actual expenses exceed the total grant budget.';
      }

      // Validate quarterly budgets and actuals
      quarters.forEach((q) => {
        const budgetValue = parseFloat(String(budgetValues[`${rowId}-${q}`] ?? '0'));
        if (budgetValue < 0) {
          errors[`budget-${q}`] = `${q} Budget cannot be negative.`;
        }
        const actualValue = parseFloat(String(actualValues[`${rowId}-${q}`] ?? '0'));
        if (actualValue < 0) {
          errors[`actual-${q}`] = `${q} Actual cannot be negative.`;
        }
      });

      if (Object.keys(errors).length > 0) {
        newSubmissionErrors[rowId] = errors;
      }
    });

    setSubmissionErrors((prev) => {
      const updatedErrors = { ...prev };
      Object.keys(updatedErrors).forEach((rowId) => {
        if (!newSubmissionErrors[parseInt(rowId)]) {
          delete updatedErrors[parseInt(rowId)];
        }
      });
      return { ...updatedErrors, ...newSubmissionErrors };
    });

    // Combine all errors for popup
    const allErrorsSet = new Set<string>([
      ...Object.entries(rowErrors).flatMap(([rowId, errors]) =>
        Object.entries(errors).map(([field, message]) => `Row ${rowId}: ${field}: ${message}`)
      ),
      ...Object.entries(budgetSumErrors).flatMap(([rowId, errors]) =>
        Object.entries(errors).map(([field, message]) => `Row ${rowId}: ${field}: ${message}`)
      ),
      ...Object.entries(remarksErrors).map(([rowId, message]) =>
        `Row ${rowId}: Remarks: ${message}`
      ),
      ...Object.entries(newSubmissionErrors).flatMap(([rowId, errors]) =>
        Object.entries(errors).map(([field, message]) => `Row ${rowId}: ${field}: ${message}`)
      ),
    ]);

    // setErrorPopup(`Please fix the following validation errors:\n${[...allErrorsSet].join('\n')}`);

    if (allErrorsSet.size > 0) {
      // Show error popup but keep rows collapsed - users can manually expand to see details
      setErrorPopup(
        `Please fix the following validation errors:\n${[...allErrorsSet].join('\n')}`
      );
      setIsSubmitting(false);
      return;
    }

    try {
      const expensesToSubmit = updatedExcelData.map((row) => {
        const rowId = row.id;
        const updatedBudgetQuarterly = { ...row.budget_quarterly };
        const updatedActualQuarterly = { ...row.actual_quarterly };

        quarters.forEach((q) => {
          updatedBudgetQuarterly[q] =
            parseFloat(String(budgetValues[`${rowId}-${q}`] ?? '0'));
          updatedActualQuarterly[q] =
            parseFloat(String(actualValues[`${rowId}-${q}`] ?? '0'));
        });

        return {
          id: rowId,
          particulars: row.particulars || '',
          main_header: row.main_header || '',
          sub_header: row.sub_header || '',
          units: parseFloat(row.units) || 0,
          frequency: row.frequency || '',
          cost_per_unit: parseFloat(row.cost_per_unit) || 0,
          activity_description: row.activity_description || '',
          budget_q1: updatedBudgetQuarterly['Apr-Jun'],
          budget_q2: updatedBudgetQuarterly['Jul-Sep'],
          budget_q3: updatedBudgetQuarterly['Oct-Dec'],
          budget_q4: updatedBudgetQuarterly['Jan-Mar'],
          actual_q1: updatedActualQuarterly['Apr-Jun'],
          actual_q2: updatedActualQuarterly['Jul-Sep'],
          actual_q3: updatedActualQuarterly['Oct-Dec'],
          actual_q4: updatedActualQuarterly['Jan-Mar'],
          grant: parseInt(row.grant, 10),
          remarks: remarksValues[rowId] || '',
          metadata: {
            edit_history: editHistory
              .filter((log) => log.row_id === rowId)
              .map((log) => ({
                row_id: log.row_id,
                field: log.field,
                old_value: log.old_value,
                new_value: log.new_value,
                timestamp: log.timestamp,
              })),
          },
        };
      });

      console.log('Submitting expenses:', JSON.stringify(expensesToSubmit, null, 2));
      let response = null;
      if (doesSelectedGrantHaveExpenses) {
        console.log("Bulk updating expenses");
        response = await bulkUpdateExpenses(expensesToSubmit);
        setSuccessPopup('Expenses updated successfully!');
      } else {
        console.log("Bulk creating expenses");
        response = await createExpense(expensesToSubmit);
        setSuccessPopup('Expenses created successfully!');
      }
      console.log('Backend response:', response);
      setShouldUpdateExpenses(Date.now().toString());
      setEditHistory([]);
      setSubmissionErrors({});
      if (isClient) {
        localStorage.removeItem('editHistory');
        localStorage.removeItem('uploadedExcelFile');
      }
      setTimeout(() => {
        setSuccessPopup(null);
        router.back();
      }, 1500);
    } catch (error: any) {
      let errorMessage = 'Failed to submit expenses.';
      if (error.response && error.response.data) {
        const errors = error.response.data;
        const newRemarksErrors: Record<number, string> = {};
        const newSubmissionErrors: Record<number, Record<string, string>> = {};
        if (Array.isArray(errors)) {
          errors.forEach((err: any, idx: number) => {
            const rowId = idx + 1;
            const fieldErrors: Record<string, string> = {};

            if (err.errors) {
              Object.entries(err.errors).forEach(([field, messages]) => {
                if (field === 'remarks') {
                  return;
                }
                fieldErrors[field.replace('/_/g', '-')] = Array.isArray(messages) ? messages.join(', ') : String(messages);
              });
            }

            newSubmissionErrors[rowId] = fieldErrors;
            if (err.errors.remarks) {
              newRemarksErrors[rowId] =
                err.errors.remarks[0] || 'Remarks are required when actual expenses exceed the total grant budget.';
            }
            if (err.non_field_errors) {
              newSubmissionErrors[rowId].non_field_errors = err.non_field_errors.join(', ');
            }
          });
          setRemarksErrors((prev) => ({ ...prev, ...newRemarksErrors }));
          setSubmissionErrors((prev) => ({ ...prev, ...newSubmissionErrors }));
          // Rows remain collapsed - users can manually expand to see error details

          const errorMessages = errors
            .map((err: any, idx: number) => {
              const rowId = idx + 1;
              let messages: string[] = [];

              if (err.errors) {
                for (const [field, msg] of Object.entries(err.errors)) {
                  const readableField = field.replace(/_/g, '-');
                  const flatMsg = Array.isArray(msg) ? msg.join(', ') : String(msg);
                  messages.push(`${readableField}: ${flatMsg}`);
                }
              }

              if (err.remarks) {
                messages.push(`remarks: ${Array.isArray(err.remarks) ? err.remarks.join(', ') : err.remarks}`);
              }

              if (err.non_field_errors) {
                messages.push(`non_field_errors: ${Array.isArray(err.non_field_errors) ? err.non_field_errors.join(', ') : err.non_field_errors}`);
              }

              return `Row ${rowId}: ${messages.join('; ')}`;
            })
            .join('\n');
          errorMessage = `Backend validation errors:\n\n${errorMessages.split('; ').join('\n')}`;
        } else {
          errorMessage = `Failed to submit expenses: ` + errors;
        }
      } else {
        errorMessage = `Failed to submit expenses: ${error.message}`;
      }
      setErrorPopup(errorMessage);
    } finally {
      setIsSubmitting(false);
    }
  };

  const openHistoryModal = (rowId: number | null = null) => {
    setHistoryRowId(rowId);
    setIsHistoryModalOpen(true);
  };

  const closeHistoryModal = () => {
    setIsHistoryModalOpen(false);
    setHistoryRowId(null);
  };

  const navigateToUpdateAll = () => {
    router.push('/funding/expenses/update');
  };


  return (
    <div className="w-full min-h-screen flex font-sans">
        <div className="w-full rounded-2xl">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-800">Expenses Management</h1>
              <p className="text-sm text-gray-500 mt-1">Track and manage project expenses</p>
            </div>
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-1">
                <FileText className="h-4 w-4 text-teal-500" />
                <span className="text-sm text-gray-600">Grant:</span>
              </div>

              <Select value={selectedGrant} onValueChange={setSelectedGrant}>
                <SelectTrigger className='border border-teal-200 focus:ring-teal-500 '>
                  <SelectValue placeholder='Select a grant' />
                </SelectTrigger>
                <SelectContent>
                  {grants.map((grant) => (
                    <SelectItem key={grant.id}
                      value={grant.id}
                    >
                      {grant.grant_name}
                    </SelectItem>
                  ))}
                </SelectContent>

              </Select>

            </div>
          </div>


        {errorPopup && (
          <ErrorPopup
            errorPopup={errorPopup}
            setErrorPopup={setErrorPopup}
          />


        )}

        {successPopup && (
          <SuccessPopup
            successPopup={successPopup}
            setSuccessPopup={setSuccessPopup}
          />
        )}

        {isUploadModalOpen && (
          <ExcelUploadModal
            isOpen={isUploadModalOpen}
            onClose={() => setIsUploadModalOpen(false)}
            onFileUpload={handleFileUpload}
          />

        )}

        {updatedExcelData.length > 0 && (
          <div className="mt-8">
            {/* Expense Table with integrated quarterly totals */}
            <ExpenseTable
            updatedExcelData={updatedExcelData}
            rowErrors={rowErrors}
            budgetSumErrors={budgetSumErrors}
            remarksErrors={remarksErrors}
            submissionErrors={submissionErrors}
            actualValues={actualValues}
            budgetValues={budgetValues}
            remarksValues={remarksValues}
            editHistory={editHistory}
            currentQuarter={currentQuarter}
            totalGrantBudgets={totalGrantBudgets}
            quarters={quarters}
            toggleRow={toggleRow}
            expandedRows={expandedRows}
            handleBudgetChange={handleBudgetChange}
            handleActualChange={handleActualChange}
            handleRemarksChange={handleRemarksChange}
            validateNumber={validateNumber}
            isBudgetEditable={isBudgetEditable}
            isActualEditable={isActualEditable}
            calculateTotal={calculateTotal}
            onSubmit={handleSubmit}
            isSubmitting={isSubmitting}
          />
          </div>
        )}

        {excelData.length === 0 && (
          <div>
            <div className="text-center py-12">
              <p className="text-gray-500 text-lg">
                No expenses uploaded. Please upload an Excel file to begin.
              </p>
            </div>
            <div className="flex justify-center gap-4">
              <Button
                onClick={() => setIsUploadModalOpen(true)}
                className="bg-gradient-to-r from-teal-600 to-emerald-600 hover:from-teal-700 hover:to-emerald-700 text-white px-8 py-3 rounded-xl font-semibold shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 flex items-center gap-2"
                disabled={isLoadingGrants || grants.length === 0 || isSubmitting}
              >
                <FileSpreadsheet className="w-5 h-5" />
                Upload Excel File
              </Button>
            </div>
          </div>
        )}

        <div className="mt-8">

          <ExpensesHistoryTable refreshTrigger={shouldUpdateExpenses} selectedGrant={selectedGrant} />
        </div>
      </div>
    </div>
  );
}
