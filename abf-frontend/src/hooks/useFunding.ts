import { useState, useEffect } from 'react';
import {
  fundingService,
  GrantSummary,
  ExpenseBreakdown,
  MonthlyExpense,
  ExpenseRecord,
  DisbursementSummary,
  UpcomingDisbursement,
  DisbursementHistory,
  MonthlyDisbursement
} from '@/services/funding-service';
import * as granteeExpenseService from '@/services/grantee-expense-service';

export function useFunding() {
  // Common states
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Budget overview states
  const [grantSummary, setGrantSummary] = useState<GrantSummary | null>(null);
  const [expenseBreakdown, setExpenseBreakdown] = useState<ExpenseBreakdown[]>([]);
  const [monthlyExpenses, setMonthlyExpenses] = useState<MonthlyExpense[]>([]);

  // Expense states
  const [expenseHistory, setExpenseHistory] = useState<ExpenseRecord[]>([]);

  // Disbursement states
  const [disbursementSummary, setDisbursementSummary] = useState<DisbursementSummary | null>(null);
  const [upcomingDisbursements, setUpcomingDisbursements] = useState<UpcomingDisbursement[]>([]);
  const [disbursementHistory, setDisbursementHistory] = useState<DisbursementHistory[]>([]);
  const [monthlyDisbursements, setMonthlyDisbursements] = useState<MonthlyDisbursement[]>([]);

  // Fetch all funding data
  const fetchFundingData = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const [
        summary,
        breakdown,
        monthly,
        history,
        disbursementSum,
        upcomingDisb,
        disbHistory,
        monthlyDisb
      ] = await Promise.all([
        fundingService.getGrantSummary(),
        fundingService.getExpenseBreakdown(),
        fundingService.getMonthlyExpenses(),
        granteeExpenseService.getExpenseHistory(), // Use the dedicated grantee expense service
        fundingService.getDisbursementSummary(),
        fundingService.getUpcomingDisbursements(),
        fundingService.getDisbursementHistory(),
        fundingService.getMonthlyDisbursements()
      ]);

      setGrantSummary(summary);
      setExpenseBreakdown(breakdown);
      setMonthlyExpenses(monthly);
      setExpenseHistory(history);
      setDisbursementSummary(disbursementSum);
      setUpcomingDisbursements(upcomingDisb);
      setDisbursementHistory(disbHistory);
      setMonthlyDisbursements(monthlyDisb);
    } catch (err) {
      console.error('Error fetching funding data:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch funding data');
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch only budget overview data
  const fetchBudgetData = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const [summary, breakdown, monthly] = await Promise.all([
        fundingService.getGrantSummary(),
        fundingService.getExpenseBreakdown(),
        fundingService.getMonthlyExpenses()
      ]);

      setGrantSummary(summary);
      setExpenseBreakdown(breakdown);
      setMonthlyExpenses(monthly);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch budget data');
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch only expense data
  const fetchExpenseData = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const [breakdown, monthly, history] = await Promise.all([
        fundingService.getExpenseBreakdown(),
        fundingService.getMonthlyExpenses(),
        granteeExpenseService.getExpenseHistory() // Use the dedicated grantee expense service
      ]);

      console.log('Fetched expense history for quarterly chart:', history.length, 'records');
      console.log('Fetched expense history for quarterly chart:', history.length, 'records');
      setExpenseBreakdown(breakdown);
      setMonthlyExpenses(monthly);
      setExpenseHistory(history);
    } catch (err) {
      console.error('Error fetching expense data:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch expense data');
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch only disbursement data
  const fetchDisbursementData = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const [disbursementSum, upcomingDisb, disbHistory, monthlyDisb] = await Promise.all([
        fundingService.getDisbursementSummary(),
        fundingService.getUpcomingDisbursements(),
        fundingService.getDisbursementHistory(),
        fundingService.getMonthlyDisbursements()
      ]);

      setDisbursementSummary(disbursementSum);
      setUpcomingDisbursements(upcomingDisb);
      setDisbursementHistory(disbHistory);
      setMonthlyDisbursements(monthlyDisb);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch disbursement data');
    } finally {
      setIsLoading(false);
    }
  };

  // Submit expense
  const submitExpense = async (formData: FormData) => {
    try {
      // Use the dedicated grantee expense service for submitting expenses
      const result = await granteeExpenseService.submitExpense(formData);
      console.log('Expense submission result:', result);

      // Wait a moment to ensure the backend has processed the data
      setTimeout(async () => {
        await fetchExpenseData(); // Refresh only expense data after submission
      }, 1000);

      return result;
    } catch (err) {
      console.error('Error submitting expense:', err);
      throw new Error(err instanceof Error ? err.message : 'Failed to submit expense');
    }
  };

  // Initial data fetch
  useEffect(() => {
    fetchFundingData();
  }, []);

  // Get quarterly expense data for Budget vs Actuals chart
  const getQuarterlyExpenseData = () => {
    // Use real expense history data if available
    if (!expenseHistory || expenseHistory.length === 0) {
      console.log('No expense history available, returning empty quarterly data');
      return [
        { quarter: 'Q1', budget: 0, actual: 0 },
        { quarter: 'Q2', budget: 0, actual: 0 },
        { quarter: 'Q3', budget: 0, actual: 0 },
        { quarter: 'Q4', budget: 0, actual: 0 }
      ];
    }

    console.log('Processing expense history for quarterly chart:', expenseHistory.length, 'records');

    // Initialize quarterly totals
    const quarterlyTotals = {
      Q1: { budget: 0, actual: 0 },
      Q2: { budget: 0, actual: 0 },
      Q3: { budget: 0, actual: 0 },
      Q4: { budget: 0, actual: 0 }
    };

    // Aggregate expense data by quarters from real backend data
    expenseHistory.forEach(expense => {
      quarterlyTotals.Q1.budget += expense.budget_q1 || 0;
      quarterlyTotals.Q1.actual += expense.actual_q1 || 0;
      quarterlyTotals.Q2.budget += expense.budget_q2 || 0;
      quarterlyTotals.Q2.actual += expense.actual_q2 || 0;
      quarterlyTotals.Q3.budget += expense.budget_q3 || 0;
      quarterlyTotals.Q3.actual += expense.actual_q3 || 0;
      quarterlyTotals.Q4.budget += expense.budget_q4 || 0;
      quarterlyTotals.Q4.actual += expense.actual_q4 || 0;
    });

    console.log('Calculated quarterly totals:', quarterlyTotals);

    // Convert to array format for chart
    const chartData = [
      { quarter: 'Q1', budget: quarterlyTotals.Q1.budget, actual: quarterlyTotals.Q1.actual },
      { quarter: 'Q2', budget: quarterlyTotals.Q2.budget, actual: quarterlyTotals.Q2.actual },
      { quarter: 'Q3', budget: quarterlyTotals.Q3.budget, actual: quarterlyTotals.Q3.actual },
      { quarter: 'Q4', budget: quarterlyTotals.Q4.budget, actual: quarterlyTotals.Q4.actual }
    ];

    console.log('Chart data for quarterly chart:', chartData);
    return chartData;
  };

  return {
    // States
    isLoading,
    error,
    grantSummary,
    expenseBreakdown,
    monthlyExpenses,
    expenseHistory,
    disbursementSummary,
    upcomingDisbursements,
    disbursementHistory,
    monthlyDisbursements,

    // Actions
    submitExpense,
    refreshData: fetchFundingData,
    refreshBudgetData: fetchBudgetData,
    refreshExpenseData: fetchExpenseData,
    refreshDisbursementData: fetchDisbursementData,

    // Helpers
    formatCurrency: fundingService.formatCurrency,
    calculatePercentage: fundingService.calculatePercentage,
    getQuarterlyExpenseData
  };
}