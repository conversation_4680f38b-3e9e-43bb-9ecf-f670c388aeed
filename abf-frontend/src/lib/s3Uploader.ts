import apiClient from "@/lib/apiClient";

export async function getPresignedUrl(file: File) {
  const res = await apiClient.post("/api/aws/v1/s3/generate-presigned-url/", {
    file_name: file.name,
    file_type: file.type,
  });
  console.log("Presigned URL response = " + JSON.stringify(res.data.data));
  return res.data.data;
}

export async function getPresignedUrlForProfileLogo(file: File) {
  console.log("File type = " + file.type);
  console.log("File name = " + file.name);
  const res = await apiClient.post("/api/aws/v1/s3/generate-presigned-url/profile-logo/", {
    file_name: file.name,
    file_type: file.type,
  });
  return res.data.data;

}

export async function uploadFileToS3(file: File, uploadUrl: string){
  console.log("IN Upload url and file type = " + file.type);
  const response = await fetch(uploadUrl, {
    method: "PUT",
    headers: { "Content-Type":  "image/png"},
    body: file,
  });
  console.log("Upload response = " + JSON.stringify(response));
  return response;
}