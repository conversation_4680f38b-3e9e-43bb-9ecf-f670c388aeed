import React, { useState } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { ExpenseBreakdown } from '@/services/funding-service';

interface ExpenseManagementProps {
  organization: {
    expenses?: any[];
  };
}

export function ExpenseManagement({ organization }: ExpenseManagementProps) {
  const [activeTab, setActiveTab] = useState<'manual' | 'excel'>('manual');
  const [selectedYear, setSelectedYear] = useState<string>('2023');
  const [expenses, setExpenses] = useState<any[]>(organization.expenses || []);
  const [file, setFile] = useState<File | null>(null);
  const [isUploading, setIsUploading] = useState(false);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      maximumFractionDigits: 0
    }).format(amount);
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const selectedFile = e.target.files[0];
      const fileExtension = selectedFile.name.split('.').pop()?.toLowerCase();

      if (fileExtension === 'xlsx' || fileExtension === 'xls' || fileExtension === 'csv') {
        setFile(selectedFile);
      } else {
        alert('Please upload a valid Excel or CSV file');
      }
    }
  };

  const handleUpload = async () => {
    if (!file) {
      alert('Please select a file to upload');
      return;
    }

    setIsUploading(true);

    try {
      // TODO: Implement actual file upload and processing
      const mockUploadedExpenses = [
        {
          sr_no: 1,
          particulars: 'Staff Salaries',
          main_header: 'Human Resources',
          sub_headers: 'Permanent Staff',
          units: 'Months',
          frequency: '12',
          cost_per_unit: 50000,
          budget_quarterly: { Q1: 150000, Q2: 150000, Q3: 150000, Q4: 150000 },
          actuals_quarterly: { Q1: 145000, Q2: 150000, Q3: 148000, Q4: 0 },
          total_budget: 600000,
          total_actual: 443000,
          year: selectedYear
        }
      ];

      setExpenses(mockUploadedExpenses);
      setActiveTab('excel');
    } catch (error) {
      console.error('Error uploading file:', error);
      alert('Error uploading file. Please try again.');
    } finally {
      setIsUploading(false);
    }
  };

  const handleAddRow = () => {
    const newRow = {
      sr_no: expenses.length + 1,
      particulars: '',
      main_header: '',
      sub_headers: '',
      units: '',
      frequency: '',
      cost_per_unit: 0,
      budget_quarterly: { Q1: 0, Q2: 0, Q3: 0, Q4: 0 },
      actuals_quarterly: { Q1: 0, Q2: 0, Q3: 0, Q4: 0 },
      total_budget: 0,
      total_actual: 0,
      year: selectedYear
    };

    setExpenses([...expenses, newRow]);
  };

  const calculateTotals = () => {
    return expenses.reduce((acc, expense) => ({
      totalBudget: acc.totalBudget + (expense.total_budget || 0),
      totalActual: acc.totalActual + (expense.total_actual || 0),
      q1Budget: acc.q1Budget + (expense.budget_quarterly?.Q1 || 0),
      q2Budget: acc.q2Budget + (expense.budget_quarterly?.Q2 || 0),
      q3Budget: acc.q3Budget + (expense.budget_quarterly?.Q3 || 0),
      q4Budget: acc.q4Budget + (expense.budget_quarterly?.Q4 || 0),
      q1Actual: acc.q1Actual + (expense.actuals_quarterly?.Q1 || 0),
      q2Actual: acc.q2Actual + (expense.actuals_quarterly?.Q2 || 0),
      q3Actual: acc.q3Actual + (expense.actuals_quarterly?.Q3 || 0),
      q4Actual: acc.q4Actual + (expense.actuals_quarterly?.Q4 || 0)
    }), {
      totalBudget: 0, totalActual: 0,
      q1Budget: 0, q2Budget: 0, q3Budget: 0, q4Budget: 0,
      q1Actual: 0, q2Actual: 0, q3Actual: 0, q4Actual: 0
    });
  };

  const totals = calculateTotals();

  return (
    <Card className="bg-white shadow-md hover:shadow-lg transition-shadow mb-8">
      <CardHeader>
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
          <div>
            <CardTitle className="text-xl text-gray-800">Expense Management</CardTitle>
            <CardDescription>Track and manage project expenses</CardDescription>
          </div>
          <div className="flex space-x-2">
            <Button
              onClick={() => setActiveTab('manual')}
              variant={activeTab === 'manual' ? 'default' : 'outline'}
              className={activeTab === 'manual' ? 'bg-orange-500 hover:bg-orange-600' : ''}
            >
              Manual Entry
            </Button>
            <Button
              onClick={() => setActiveTab('excel')}
              variant={activeTab === 'excel' ? 'default' : 'outline'}
              className={activeTab === 'excel' ? 'bg-orange-500 hover:bg-orange-600' : ''}
            >
              Excel Upload
            </Button>
          </div>
        </div>
      </CardHeader>

      <CardContent>
        <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as 'manual' | 'excel')}>
          <TabsList>
            <TabsTrigger value="manual">Manual Entry</TabsTrigger>
            <TabsTrigger value="excel">Excel Upload</TabsTrigger>
          </TabsList>
          
          <TabsContent value="manual">
            <div className="space-y-6">
              <div className="flex justify-between items-center">
                <h3 className="text-lg font-medium text-gray-800">Manual Expense Entry</h3>
                <Button
                  onClick={handleAddRow}
                  className="bg-orange-500 hover:bg-orange-600 flex items-center gap-1"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                  </svg>
                  Add Row
                </Button>
              </div>
              
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase">Sr no.</th>
                      <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase">Particulars</th>
                      <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase">Main header</th>
                      <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase">Sub-headers</th>
                      <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase">Units</th>
                      <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase">Frequency</th>
                      <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase">Cost per unit</th>
                      <th className="px-3 py-3 text-center text-xs font-medium text-gray-500 uppercase" colSpan={4}>Budget quarterly</th>
                      <th className="px-3 py-3 text-center text-xs font-medium text-gray-500 uppercase">Total Budget</th>
                      <th className="px-3 py-3 text-center text-xs font-medium text-gray-500 uppercase" colSpan={4}>Actuals quarterly</th>
                      <th className="px-3 py-3 text-center text-xs font-medium text-gray-500 uppercase">Total Actual</th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {expenses.map((expense, index) => (
                      <tr key={index} className={index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
                        <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-900">{expense.sr_no}</td>
                        <td className="px-3 py-2 whitespace-nowrap">
                          <Input
                            type="text"
                            value={expense.particulars}
                            onChange={(e) => {
                              const updatedExpenses = [...expenses];
                              updatedExpenses[index].particulars = e.target.value;
                              setExpenses(updatedExpenses);
                            }}
                            className="w-full text-sm"
                          />
                        </td>
                        <td className="px-3 py-2 whitespace-nowrap">
                          <Input
                            type="text"
                            value={expense.main_header}
                            onChange={(e) => {
                              const updatedExpenses = [...expenses];
                              updatedExpenses[index].main_header = e.target.value;
                              setExpenses(updatedExpenses);
                            }}
                            className="w-full text-sm"
                          />
                        </td>
                        {/* Add other editable fields similarly */}
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="excel">
            <div className="space-y-6">
              <div className="bg-gray-50 p-6 rounded-lg border border-gray-200">
                <h3 className="text-lg font-medium text-gray-800 mb-4">Excel Upload</h3>
                <p className="text-gray-600 mb-4">Upload an Excel file (.xlsx, .xls) or CSV file (.csv) with expense data.</p>
                <div className="flex items-center space-x-4">
                  <Input
                    type="file"
                    accept=".xlsx,.xls,.csv"
                    onChange={handleFileChange}
                    className="max-w-md"
                  />
                  <Button
                    onClick={handleUpload}
                    disabled={!file || isUploading}
                    className="bg-orange-500 hover:bg-orange-600"
                  >
                    {isUploading ? 'Uploading...' : 'Upload File'}
                  </Button>
                </div>
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}