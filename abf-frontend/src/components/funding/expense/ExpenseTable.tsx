import { Fragment, useEffect, useRef, useState } from 'react';
import { ChevronDown, ChevronUp, Send } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import QuarterInlineInput from './QuarterInlineInput';
import RemarksInput from './RemarksInput';

interface ExpenseTableProps {
  updatedExcelData: any[];
  budgetValues: Record<string, number>;
  actualValues: Record<string, number>;
  remarksValues: Record<number, string>;
  rowErrors: Record<number, Record<string, string>>;
  budgetSumErrors: Record<number, Record<string, string>>;
  remarksErrors: Record<number, string>;
  submissionErrors: Record<number, Record<string, string>>;
  totalGrantBudgets: Record<number, number>;
  currentQuarter: number;
  quarters: string[];
  expandedRows: number[];
  editHistory: any[];
  handleBudgetChange: (rowId: number, quarter: string, value: number) => void;
  handleActualChange: (rowId: number, quarter: string, value: number) => void;
  handleRemarksChange: (rowId: number, value: string) => void;
  isBudgetEditable: (quarter: string) => boolean;
  isActualEditable: (quarter: string) => boolean;
  validateNumber: (value: number, label: string) => { isValid: boolean; message: string };
  calculateTotal: (rowId: number, type: 'budget' | 'actual') => string;
  toggleRow: (rowId: number) => void;
  onSubmit?: () => void;
  isSubmitting?: boolean;
}

export default function ExpenseTable({
  updatedExcelData,
  budgetValues,
  actualValues,
  remarksValues,
  rowErrors,
  budgetSumErrors,
  remarksErrors,
  submissionErrors,
  totalGrantBudgets,
  currentQuarter,
  quarters,
  expandedRows,
  editHistory,
  handleBudgetChange,
  handleActualChange,
  handleRemarksChange,
  isBudgetEditable,
  isActualEditable,
  validateNumber,
  calculateTotal,
  toggleRow,
  onSubmit,
  isSubmitting = false,
}: ExpenseTableProps) {
  const expandedRowRefs = useRef<Record<number, HTMLDivElement | null>>({});
  const [expandedSections, setExpandedSections] = useState<Record<string, boolean>>({
    'Program Costs': true,
    'Non-Program Costs': true,
  });

  const toggleSection = (sectionTitle: string) => {
    setExpandedSections(prev => ({
      ...prev,
      [sectionTitle]: !prev[sectionTitle]
    }));
  };

  useEffect(() => {
    expandedRows.forEach(rowId => {
      const rowRef = expandedRowRefs.current[rowId];
      if (rowRef) {
        setTimeout(() => {
          rowRef.scrollIntoView({
            behavior: 'smooth',
            block: 'center',
            inline: 'nearest'
          });
        }, 100);
      }
    });
  }, [expandedRows]);

  // Calculate totals for the top component
  const grandTotals = updatedExcelData.reduce((acc, row) => {
    const rowId = row.id;
    return {
      q1Budget: acc.q1Budget + (budgetValues[`${rowId}-Apr-Jun`] || 0),
      q2Budget: acc.q2Budget + (budgetValues[`${rowId}-Jul-Sep`] || 0),
      q3Budget: acc.q3Budget + (budgetValues[`${rowId}-Oct-Dec`] || 0),
      q4Budget: acc.q4Budget + (budgetValues[`${rowId}-Jan-Mar`] || 0),
      q1Actual: acc.q1Actual + (actualValues[`${rowId}-Apr-Jun`] || 0),
      q2Actual: acc.q2Actual + (actualValues[`${rowId}-Jul-Sep`] || 0),
      q3Actual: acc.q3Actual + (actualValues[`${rowId}-Oct-Dec`] || 0),
      q4Actual: acc.q4Actual + (actualValues[`${rowId}-Jan-Mar`] || 0),
    };
  }, {
    q1Budget: 0, q2Budget: 0, q3Budget: 0, q4Budget: 0,
    q1Actual: 0, q2Actual: 0, q3Actual: 0, q4Actual: 0,
  });

  const totalBudget = grandTotals.q1Budget + grandTotals.q2Budget + grandTotals.q3Budget + grandTotals.q4Budget;
  const totalActual = grandTotals.q1Actual + grandTotals.q2Actual + grandTotals.q3Actual + grandTotals.q4Actual;

  return (
    <div className="mt-8 space-y-6">
      {/* Quarterly Totals Summary - Enhanced Professional Design */}
      <Card className="rounded-3xl shadow-2xl bg-gradient-to-br from-white via-slate-50/80 to-slate-100/40 border-2 border-slate-200/50 overflow-hidden">
        <div className="bg-gradient-to-r from-slate-800 via-slate-700 to-slate-800 px-6 py-4 shadow-lg">
          <h3 className="text-lg font-bold text-white text-center tracking-wider uppercase">Quarterly Totals</h3>
        </div>

        <div className="p-8 bg-white">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Budget Section */}
            <div className="space-y-6">
              <div className="flex items-center gap-3 mb-4">
                <div className="w-1 h-8 bg-gradient-to-b from-teal-600 to-emerald-600 rounded-full shadow-lg"></div>
                <h4 className="text-base font-bold text-slate-800 uppercase tracking-wide">Budget</h4>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div className="bg-gradient-to-br from-slate-50 to-slate-100 p-4 rounded-xl border-2 border-slate-200/60 hover:shadow-lg transition-all duration-300 hover:scale-105">
                  <div className="text-center">
                    <p className="text-sm font-bold text-slate-600 mb-1">Q1</p>
                    <p className="text-base font-bold text-slate-800">₹{grandTotals.q1Budget.toLocaleString()}</p>
                  </div>
                </div>
                <div className="bg-gradient-to-br from-slate-50 to-slate-100 p-4 rounded-xl border-2 border-slate-200/60 hover:shadow-lg transition-all duration-300 hover:scale-105">
                  <div className="text-center">
                    <p className="text-sm font-bold text-slate-600 mb-1">Q2</p>
                    <p className="text-base font-bold text-slate-800">₹{grandTotals.q2Budget.toLocaleString()}</p>
                  </div>
                </div>
                <div className="bg-gradient-to-br from-slate-50 to-slate-100 p-4 rounded-xl border-2 border-slate-200/60 hover:shadow-lg transition-all duration-300 hover:scale-105">
                  <div className="text-center">
                    <p className="text-sm font-bold text-slate-600 mb-1">Q3</p>
                    <p className="text-base font-bold text-slate-800">₹{grandTotals.q3Budget.toLocaleString()}</p>
                  </div>
                </div>
                <div className="bg-gradient-to-br from-slate-50 to-slate-100 p-4 rounded-xl border-2 border-slate-200/60 hover:shadow-lg transition-all duration-300 hover:scale-105">
                  <div className="text-center">
                    <p className="text-sm font-bold text-slate-600 mb-1">Q4</p>
                    <p className="text-base font-bold text-slate-800">₹{grandTotals.q4Budget.toLocaleString()}</p>
                  </div>
                </div>
              </div>
              <div className="bg-gradient-to-r from-slate-700 via-slate-600 to-slate-700 p-4 rounded-xl shadow-xl">
                <div className="text-center">
                  <p className="text-sm font-bold text-slate-200 mb-1 uppercase tracking-wider">Total Budget</p>
                  <p className="text-xl font-bold text-white">₹{totalBudget.toLocaleString()}</p>
                </div>
              </div>
            </div>

            {/* Actual Section */}
            <div className="space-y-6">
              <div className="flex items-center gap-3 mb-4">
                <div className="w-1 h-8 bg-gradient-to-b from-emerald-600 to-teal-600 rounded-full shadow-lg"></div>
                <h4 className="text-base font-bold text-slate-800 uppercase tracking-wide">Actual</h4>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div className="bg-gradient-to-br from-slate-50 to-slate-100 p-4 rounded-xl border-2 border-slate-200/60 hover:shadow-lg transition-all duration-300 hover:scale-105">
                  <div className="text-center">
                    <p className="text-sm font-bold text-slate-600 mb-1">Q1</p>
                    <p className="text-base font-bold text-slate-800">₹{grandTotals.q1Actual.toLocaleString()}</p>
                  </div>
                </div>
                <div className="bg-gradient-to-br from-slate-50 to-slate-100 p-4 rounded-xl border-2 border-slate-200/60 hover:shadow-lg transition-all duration-300 hover:scale-105">
                  <div className="text-center">
                    <p className="text-sm font-bold text-slate-600 mb-1">Q2</p>
                    <p className="text-base font-bold text-slate-800">₹{grandTotals.q2Actual.toLocaleString()}</p>
                  </div>
                </div>
                <div className="bg-gradient-to-br from-slate-50 to-slate-100 p-4 rounded-xl border-2 border-slate-200/60 hover:shadow-lg transition-all duration-300 hover:scale-105">
                  <div className="text-center">
                    <p className="text-sm font-bold text-slate-600 mb-1">Q3</p>
                    <p className="text-base font-bold text-slate-800">₹{grandTotals.q3Actual.toLocaleString()}</p>
                  </div>
                </div>
                <div className="bg-gradient-to-br from-slate-50 to-slate-100 p-4 rounded-xl border-2 border-slate-200/60 hover:shadow-lg transition-all duration-300 hover:scale-105">
                  <div className="text-center">
                    <p className="text-sm font-bold text-slate-600 mb-1">Q4</p>
                    <p className="text-base font-bold text-slate-800">₹{grandTotals.q4Actual.toLocaleString()}</p>
                  </div>
                </div>
              </div>
              <div className="bg-gradient-to-r from-slate-700 via-slate-600 to-slate-700 p-4 rounded-xl shadow-xl">
                <div className="text-center">
                  <p className="text-sm font-bold text-slate-200 mb-1 uppercase tracking-wider">Total Actual</p>
                  <p className="text-xl font-bold text-white">₹{totalActual.toLocaleString()}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </Card>

      {/* Main Expense Table */}
      <Card className="rounded-3xl shadow-2xl bg-gradient-to-br from-white via-slate-50/80 to-teal-50/40 border border-teal-200/30 backdrop-blur-sm overflow-hidden">
        <div className="p-2">
          <div className="overflow-x-auto rounded-2xl border border-teal-200/30 bg-white shadow-inner">
            <table className="w-full text-base border-collapse">
              <thead className="sticky top-0 z-20">
                <tr className="bg-gradient-to-r from-slate-800 via-slate-700 to-slate-800 text-white border-b border-slate-600 shadow-lg">
                {[
                  { label: 'Sr.', width: 'w-12' },
                  { label: 'Sub Header', width: 'w-32' },
                  { label: 'Particulars', width: 'w-70' },
                  { label: 'Units', width: 'w-10' },
                  { label: 'Freq', width: 'w-10' },
                  { label: 'Cost per Unit', width: 'w-24' },
                  { label: 'Total Budget', width: 'w-28' },
                  { label: 'Current Quarter', width: 'w-32' },
                  { label: '', width: 'w-16' },
                ].map((head, idx) => (
                  <th key={idx} className={`${head.width} px-6 py-5 font-bold text-sm text-white text-center border-r border-slate-600/50 last:border-r-0`}>
                    <div className="flex items-center justify-center">
                      <span className="tracking-wider uppercase">{head.label}</span>
                    </div>
                  </th>
                ))}
              </tr>
            </thead>
            <tbody className="bg-white">
              {(() => {
                // Group data by main header type
                const programCosts = updatedExcelData.filter(row =>
                  row.main_header?.toLowerCase().includes('program cost') &&
                  !row.main_header?.toLowerCase().includes('non')
                );
                const nonProgramCosts = updatedExcelData.filter(row =>
                  row.main_header?.toLowerCase().includes('non program cost') ||
                  row.main_header?.toLowerCase().includes('non-program cost')
                );

                const renderRows = (data: any[], sectionTitle: string, startIndex: number) => {
                  if (data.length === 0) return null;

                  return (
                    <Fragment key={sectionTitle}>
                      {/* Section Header Row */}
                      <tr className={`${
                        sectionTitle.includes('Program')
                          ? 'bg-gradient-to-r from-teal-600 via-teal-700 to-teal-600'
                          : 'bg-gradient-to-r from-orange-600 via-orange-700 to-orange-600'
                      } border-b-2 border-white/20 shadow-lg`}>
                        <td colSpan={9} className="px-0 py-0">
                          <Button
                            variant="ghost"
                            onClick={() => toggleSection(sectionTitle)}
                            className="w-full flex items-center gap-4 hover:bg-white/10 rounded-none p-6 transition-all duration-300 text-white"
                          >
                            <div className={`w-1 h-8 rounded-full bg-white shadow-lg`}></div>
                            <h3 className="text-base font-bold text-white uppercase tracking-wider flex-1 text-left">
                              {sectionTitle}
                            </h3>
                            <span className="text-sm font-semibold text-white bg-white/20 px-4 py-2 rounded-full border border-white/30 backdrop-blur-sm">
                              {data.length} items
                            </span>
                            {expandedSections[sectionTitle] ? (
                              <ChevronUp className="w-5 h-5 text-white" />
                            ) : (
                              <ChevronDown className="w-5 h-5 text-white" />
                            )}
                          </Button>
                        </td>
                      </tr>

                      {/* Data Rows */}
                      {expandedSections[sectionTitle] && data.map((row, index) => {
                        const rowId = row.id;
                        const rowError = rowErrors[rowId] || {};
                        const budgetSumError = budgetSumErrors[rowId] || {};
                        const remarkError = remarksErrors[rowId] || '';
                        const submissionError = submissionErrors[rowId] || {};
                        const hasRowError =
                          Object.keys(rowError).length > 0 ||
                          Object.keys(budgetSumError).length > 0 ||
                          remarkError ||
                          Object.keys(submissionError).length > 0;
                        const currentQuarterLabel = quarters[currentQuarter - 1];
                        const totalGrantBudget = totalGrantBudgets[rowId] || 0;
                        const isExpanded = expandedRows.includes(rowId);
                        const actualIndex = startIndex + index;

                        const isProgramCost = sectionTitle.includes('Program');
                        const isNonProgramCost = sectionTitle.includes('Non-Program');

                return (
                  <Fragment key={rowId}>
                    <tr
                      className={`group transition-all duration-300 border-b border-gray-200/40 hover:shadow-md ${
                        hasRowError
                          ? 'bg-gradient-to-r from-red-50/90 via-red-25/50 to-orange-50/70 hover:from-red-100/90 hover:via-red-50/60 hover:to-orange-100/80'
                          : isExpanded
                            ? 'bg-gradient-to-r from-slate-50/60 via-white to-slate-50/40 shadow-inner'
                            : 'bg-white hover:bg-gradient-to-r hover:from-slate-50/30 hover:via-white hover:to-slate-50/20'
                      }`}
                    >
                        <td className="w-12 px-4 py-6 text-center border-r border-gray-200/40">
                          <div className="inline-flex items-center justify-center w-9 h-9 bg-gradient-to-br from-slate-700 via-slate-600 to-slate-700 text-white text-sm font-bold rounded-full shadow-lg ring-2 ring-slate-300/50 hover:ring-slate-400/70 transition-all duration-200">
                            {String(actualIndex + 1).padStart(2, '0')}
                          </div>
                        </td>
                      <td className="w-32 px-4 py-6 border-r border-gray-200/40">
                        <div className="text-gray-800 text-sm font-medium leading-relaxed">
                          {row.sub_header || 'N/A'}
                        </div>
                      </td>
                      <td className="w-48 px-4 py-6 border-r border-gray-200/40">
                        <div className="text-gray-700 text-sm leading-relaxed">
                          {row.particulars || 'N/A'}
                        </div>
                      </td>
                      <td className="w-20 px-4 py-6 text-center border-r border-gray-200/40">
                        <div className={`text-sm font-semibold ${rowError['Units'] ? 'text-red-600' : 'text-gray-800'}`}>
                          {row.units || 'N/A'}
                        </div>
                        {rowError['Units'] && (
                          <div className="mt-1 text-xs text-red-500 font-medium">
                            {rowError['Units']}
                          </div>
                        )}
                      </td>
                      <td className="w-20 px-4 py-6 text-center border-r border-gray-200/40">
                        <div className={`text-sm font-semibold ${rowError['Frequency'] ? 'text-red-600' : 'text-gray-800'}`}>
                          {row.frequency || 'N/A'}
                        </div>
                        {rowError['Frequency'] && (
                          <div className="mt-1 text-xs text-red-500 font-medium">
                            {rowError['Frequency']}
                          </div>
                        )}
                      </td>
                      <td className="w-24 px-4 py-6 text-center border-r border-gray-200/40">
                        <div className={`text-sm font-bold ${rowError['Cost per Unit'] ? 'text-red-600' : 'text-gray-900'}`}>
                          {row.cost_per_unit
                            ? `₹${parseFloat(row.cost_per_unit).toFixed(2)}`
                            : 'N/A'}
                        </div>
                        {rowError['Cost per Unit'] && (
                          <div className="mt-1 text-xs text-red-500 font-medium">
                            {rowError['Cost per Unit']}
                          </div>
                        )}
                      </td>
                      <td className="w-28 px-4 py-6 text-center border-r border-gray-200/40">
                        <div className="bg-gradient-to-r from-slate-100 via-white to-slate-100 px-3 py-2 rounded-xl border border-slate-300/60 shadow-sm">
                          <div className="text-sm font-bold text-slate-800">
                            {row.total_grant_budget.toLocaleString('en-IN', {
                              style: 'currency',
                              currency: 'INR',
                              minimumFractionDigits: 0,
                              maximumFractionDigits: 0,
                            })}
                          </div>
                        </div>
                      </td>
                      <td className="w-32 px-4 py-6 border-r border-gray-200/40">
                        <QuarterInlineInput
                          label={currentQuarterLabel}
                          value={actualValues[`${rowId}-${currentQuarterLabel}`]}
                          onChange={(val) =>
                            handleActualChange(rowId, currentQuarterLabel, val)
                          }
                          readOnly={!isActualEditable(currentQuarterLabel)}
                          rowId={rowId}
                          type="actual"
                          totalGrantBudget={totalGrantBudget}
                          validate={(val) => validateNumber(val, currentQuarterLabel)}
                        />
                      </td>
                      <td className="w-16 px-4 py-6 text-center">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => toggleRow(rowId)}
                          className={`h-9 w-9 rounded-xl transition-all duration-300 shadow-sm ring-1 ring-slate-200 hover:ring-slate-300 ${
                            isExpanded
                              ? 'bg-gradient-to-br from-slate-100 to-slate-200 text-slate-700 hover:from-slate-200 hover:to-slate-300'
                              : 'bg-white hover:bg-gradient-to-br hover:from-slate-50 hover:to-slate-100 text-slate-600 hover:text-slate-700'
                          }`}
                        >
                          {isExpanded ? (
                            <ChevronUp className="w-3 h-3" />
                          ) : (
                            <ChevronDown className="w-3 h-3" />
                          )}
                        </Button>
                      </td>
                    </tr>

                    {isExpanded && (
                      <tr className="bg-gradient-to-br from-slate-100/50 via-white to-slate-100/30">
                        <td colSpan={9} className="p-0">
                          <div
                            ref={(el) => {
                              expandedRowRefs.current[rowId] = el;
                            }}
                            className="border-t-2 border-slate-200/60 bg-gradient-to-br from-slate-50/80 via-white to-slate-50/60 shadow-inner"
                          >
                            <div className="p-10 space-y-10">
                              {/* Enhanced error display */}
                              {(() => {
                                const combinedRowLevelErrors: Record<string, string> = {
                                  ...rowError,
                                  ...submissionErrors[rowId],
                                };
                                const seenKeys = new Set<string>();
                                const uniqueRowLevelErrors = Object.entries(combinedRowLevelErrors)
                                  .filter(([key]) => !key.startsWith('budget-') && !key.startsWith('actual-'))
                                  .filter(([key]) => {
                                    if (seenKeys.has(key)) return false;
                                    seenKeys.add(key);
                                    return true;
                                  });
                                return uniqueRowLevelErrors.length > 0 ? (
                                  <div className="bg-gradient-to-r from-red-50 to-orange-50 border-l-4 border-red-400 rounded-r-xl p-6 shadow-sm">
                                    <div className="flex items-center gap-3 mb-3">
                                      <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse"></div>
                                      <h4 className="text-red-700 font-bold text-base">Validation Issues</h4>
                                    </div>
                                    <div className="space-y-2">
                                      {uniqueRowLevelErrors.map(([key, message]) => (
                                        <p key={key} className="text-red-600 text-sm flex items-start gap-2">
                                          <span className="w-1.5 h-1.5 bg-red-500 rounded-full mt-1.5 flex-shrink-0"></span>
                                          <span>{message}</span>
                                        </p>
                                      ))}
                                    </div>
                                  </div>
                                ) : null;
                              })()}
                              <div className="grid grid-cols-1 xl:grid-cols-2 gap-10">

                                <div className="bg-white rounded-3xl border-2 border-slate-200/60 shadow-xl">
                                  <div className="p-8 border-b-2 border-slate-200/60">
                                    <div className="flex items-center gap-4">
                                      <div className="w-2 h-10 bg-gradient-to-b from-teal-600 to-emerald-600 rounded-full shadow-lg"></div>
                                      <h3 className="text-xl font-bold text-slate-800">
                                        Budget Quarterly Breakup
                                      </h3>
                                    </div>
                                  </div>
                                  <div className="p-8 space-y-8">

                                    {budgetSumError.initialSumError && (
                                      <div className="bg-amber-50 border-l-4 border-amber-400 rounded-r-xl p-6 shadow-sm">
                                        <p className="text-amber-700 text-base font-semibold flex items-center gap-3">
                                          <span className="w-2 h-2 bg-amber-500 rounded-full animate-pulse"></span>
                                          {budgetSumError.initialSumError}
                                        </p>
                                      </div>
                                    )}
                                    <div className="grid grid-cols-2 gap-6">
                                      {quarters.map((q) => (
                                        <QuarterInlineInput
                                          key={`budget-${rowId}-${q}`}
                                          label={q}
                                          value={budgetValues[`${rowId}-${q}`]}
                                          onChange={(val) => handleBudgetChange(rowId, q, val)}
                                          readOnly={!isBudgetEditable(q)}
                                          rowId={rowId}
                                          type="budget"
                                          totalGrantBudget={totalGrantBudget}
                                          hasError={!!rowError[`budget-${q}`]}
                                          errorMessage={rowError[`budget-${q}`] || ''}
                                          validate={(val) => validateNumber(val, `${q} Budget`)}
                                        />
                                      ))}
                                    </div>
                                  </div>
                                  <div className="px-6 py-4 bg-gradient-to-r from-slate-100 via-white to-slate-100 border-t-2 border-slate-200/60">
                                    <div className="text-center space-y-3">
                                      <div className="flex items-center justify-center gap-2">
                                        <div className="w-2 h-2 bg-teal-600 rounded-full"></div>
                                        <p className="text-sm text-slate-700 font-bold uppercase tracking-wider">Budget Total</p>
                                      </div>
                                      <p className={`text-xl font-bold ${budgetSumError.initialSumError ? 'text-red-600' : 'text-slate-800'}`}>
                                        {calculateTotal(rowId, 'budget')}
                                      </p>
                                      {budgetSumError.initialSumError && (
                                        <div className="flex items-center justify-center gap-2">
                                          <div className="w-1.5 h-1.5 bg-red-500 rounded-full animate-ping"></div>
                                          <span className="text-sm text-red-600 font-semibold">Error</span>
                                        </div>
                                      )}
                                    </div>
                                  </div>
                                </div>


                                <div className="bg-white rounded-3xl border-2 border-slate-200/60 shadow-xl">
                                  <div className="p-8 border-b-2 border-slate-200/60">
                                    <div className="flex items-center gap-4">
                                      <div className="w-2 h-10 bg-gradient-to-b from-emerald-600 to-teal-600 rounded-full shadow-lg"></div>
                                      <h3 className="text-xl font-bold text-slate-800">
                                        Actual Quarterly Breakup
                                      </h3>
                                    </div>
                                  </div>
                                  <div className="p-8 space-y-8">

                                    <div className="grid grid-cols-2 gap-6">
                                      {quarters.map((q) => (
                                        <QuarterInlineInput
                                          key={`actual-${rowId}-${q}`}
                                          label={q}
                                          value={actualValues[`${rowId}-${q}`]}
                                          onChange={(val) => handleActualChange(rowId, q, val)}
                                          readOnly={!isActualEditable(q)}
                                          rowId={rowId}
                                          type="actual"
                                          totalGrantBudget={totalGrantBudget}
                                          hasError={!!rowError[`actual-${q}`]}
                                          errorMessage={rowError[`actual-${q}`] || ''}
                                          validate={(val) => validateNumber(val, `${q} Actual`)}
                                        />
                                      ))}
                                    </div>
                                  </div>
                                  <div className="px-6 py-4 bg-gradient-to-r from-slate-100 via-white to-slate-100 border-t-2 border-slate-200/60">
                                    <div className="text-center space-y-3">
                                      <div className="flex items-center justify-center gap-2">
                                        <div className="w-2 h-2 bg-emerald-600 rounded-full"></div>
                                        <p className="text-sm text-slate-700 font-bold uppercase tracking-wider">Actual Total</p>
                                      </div>
                                      <p className="text-xl font-bold text-slate-800">
                                        {calculateTotal(rowId, 'actual')}
                                      </p>
                                    </div>
                                  </div>
                                </div>
                              </div>

                              <div className="bg-white rounded-3xl border-2 border-slate-200/60 shadow-xl">
                                <div className="p-6 border-b-2 border-slate-200/60">
                                  <div className="flex items-center gap-4">
                                    <div className="w-2 h-8 bg-gradient-to-b from-slate-600 to-slate-700 rounded-full shadow-lg"></div>
                                    <h3 className="text-xl font-bold text-slate-800">
                                      Remarks
                                    </h3>
                                  </div>
                                </div>
                                <div className="p-6">
                                  <RemarksInput
                                    value={remarksValues[rowId] ?? row.remarks}
                                    onChange={handleRemarksChange}
                                    rowId={rowId}
                                    hasError={!!remarksErrors[rowId]}
                                    errorMessage={remarksErrors[rowId]}
                                  />
                                </div>
                              </div>
                            </div>
                          </div>
                        </td>
                      </tr>
                    )}
                        </Fragment>
                      );
                    })}
                    </Fragment>
                  );
                };

                // Render both sections
                return (
                  <>
                    {renderRows(programCosts, 'Program Costs', 0)}
                    {renderRows(nonProgramCosts, 'Non-Program Costs', programCosts.length)}
                  </>
                );
              })()}
            </tbody>
            </table>
          </div>
        </div>

        {onSubmit && updatedExcelData.length > 0 && (
          <div className="px-8 py-6 bg-gradient-to-r from-slate-100 via-white to-slate-100 border-t-2 border-slate-200/60">
            <div className="flex justify-center">
              <Button
                onClick={onSubmit}
                disabled={isSubmitting}
                className="bg-gradient-to-r from-slate-700 via-slate-600 to-slate-700 hover:from-slate-800 hover:via-slate-700 hover:to-slate-800 text-white px-8 py-4 rounded-xl font-bold text-base transition-all duration-300 shadow-xl hover:shadow-2xl transform hover:scale-105 ring-2 ring-slate-300/50 hover:ring-slate-400/70"
              >
                {isSubmitting ? (
                  <div className="flex items-center gap-3">
                    <div className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                    <span className="tracking-wide">Submitting...</span>
                  </div>
                ) : (
                  <div className="flex items-center gap-3">
                    <Send className="w-5 h-5" />
                    <span className="tracking-wide uppercase">Submit Expenses</span>
                  </div>
                )}
              </Button>
            </div>
          </div>
        )}
      </Card>
    </div>
  );
  
}
