"use client";

import { useState, useEffect } from "react";
import { ProfileCard } from "@/components/ProfileCard";
import { EditableProfileCard } from "@/components/EditableProfileCard";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { toast } from "sonner";
import {
  uploadDocument,
  getDocuments,
  verifyDocument,
  Document,
  getDocumentTypes,
} from "@/services/document-service";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
  DialogClose,
} from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { DocumentType, Organization, OrganizationAttachment } from "@/types/profile";
import { FileText, Upload } from "lucide-react";
import { Card } from "../ui/card";
import { motion } from "framer-motion";

interface SupportingDocumentsProps {
  organization: Organization | null;
}

interface DocumentCardProps {
  document?: OrganizationAttachment | undefined;
  onUpload: () => void;
  description: string;
}

const getStatusBadge = (status: string) => {
  switch (status) {
    case "VERIFIED":
      return (
        <div className="flex items-center gap-1">
          <svg
            width="16"
            height="16"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M22 11.08V12C21.9988 14.1564 21.3005 16.2547 20.0093 17.9818C18.7182 19.709 16.9033 20.9725 14.8354 21.5839C12.7674 22.1953 10.5573 22.1219 8.53447 21.3746C6.51168 20.6273 4.78465 19.2461 3.61096 17.4371C2.43727 15.628 1.87979 13.4881 2.02168 11.3363C2.16356 9.18455 2.99721 7.13631 4.39828 5.49706C5.79935 3.85781 7.69279 2.71537 9.79619 2.24013C11.8996 1.7649 14.1003 1.98232 16.07 2.85999"
              stroke="#16A34A"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
            <path
              d="M22 4L12 14.01L9 11.01"
              stroke="#16A34A"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
          <span className="px-2 py-1 text-xs font-medium text-green-700 bg-green-100 rounded-full">
            Verified
          </span>
        </div>
      );
    case "PENDING":
      return (
        <div className="flex items-center gap-1">
          <svg
            width="16"
            height="16"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z"
              stroke="#EAB308"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
            <path
              d="M12 6V12L16 14"
              stroke="#EAB308"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
          <span className="px-2 py-1 text-xs font-medium text-yellow-700 bg-yellow-100 rounded-full">
            Pending Verification
          </span>
        </div>
      );
    case "REJECTED":
      return (
        <div className="flex items-center gap-1">
          <svg
            width="16"
            height="16"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z"
              stroke="#DC2626"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
            <path
              d="M15 9L9 15"
              stroke="#DC2626"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
            <path
              d="M9 9L15 15"
              stroke="#DC2626"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
          <span className="px-2 py-1 text-xs font-medium text-red-700 bg-red-100 rounded-full">
            Update Required
          </span>
        </div>
      );
    default:
      return null;
  }
};
const DocumentCard = ({
  document,
  onUpload,
  description,
}: DocumentCardProps) => {
  if (!document) return null;

  return (
    <div className="flex items-center justify-between p-3 border border-gray-200 rounded-md">
      <div className="flex items-center">
        <FileText className="text-teal-400 mr-2" />
        <div>
          <div className="flex items-center gap-2">
            <p className="font-medium">{document.attachmentTypeName}</p>
            {getStatusBadge(document.status)}
          </div>
          <p className="text-sm text-gray-500">{description}</p>
          {document.status === "REJECTED" && document.remarks && (
            <p className="text-sm text-teal-500 mt-1">{document.remarks}</p>
          )}
        </div>
      </div>
      <Button
        onClick={onUpload}
        variant="outline"
        className="text-teal-400 border-teal-400 hover:bg-teal-50"
      >
        {document.status === "REJECTED" ? "Re-upload" : "Upload"}
      </Button>
    </div>
  );
};

export function LegalDocuments({
  organization,
}: SupportingDocumentsProps) {
  const [documentTypes, setDocumentTypes] = useState<DocumentType[]>([]);
  const [documents, setDocuments] = useState<OrganizationAttachment[]>(organization?.attachments || []);

  const groupedDocuments = [
    {
      title: "Compliance Documents",
      keys: [
        "PAN_CARD",
        "CSR_REGISTRATION",
        "TRUST_REGISTRATION",
        "TAX_80G",
        "TAX_12A",
        "ORGANIZATION_REGISTRATION_CERTIFICATE",
        "TAX_EXEMPTION",
        "FCRA_CERTIFICATE",
        "INCORPORATION",
        "CANCELLED_CHEQUE",
      ],
    },
    {
      title: "Policy Documents",
      keys: [
        "CONFLICT_DECLARATION",
        "THEORY_OF_CHANGE",
        "CSR_FUNDS_DECLARATION",
        "POSH_POLICY",
        "CHILD_PROTECTION",
      ],
    },
    {
      title: "Financial Documents",
      keys: ["FINANCIAL_STATEMENT_LATEST", "FINANCIAL_STATEMENT_PREVIOUS", "FINANCIAL_STATEMENT_SECOND_PREVIOUS", "ANNUAL_REPORTS"],
    },
  ];

  const [isLoading, setIsLoading] = useState(true);

  // Fetch documents on component mount
  useEffect(() => {
    const fetchDocumentTypes = async () => {
      try {
        const response = await getDocumentTypes();
        if (response.status === "SUCCESS") {
          console.log("Document types fetched successfully:", response.data);
          setDocumentTypes(response.data);
        }

      } catch (error) {
        console.error("Error fetching document types:", error);
        toast.error("Failed to load document types");
      }

      setIsLoading(false);

    }
    fetchDocumentTypes();

  }, []);

  const [isUploading, setIsUploading] = useState(false);
  const [uploadDialogOpen, setUploadDialogOpen] = useState(false);
  const [selectedDocType, setSelectedDocType] = useState<string>("");
  const [selectedDocName, setSelectedDocName] = useState<string>("");
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [docId, setDocId] = useState<number | null>(null);

  const handleUploadClick = (
    docType: string,
    docName: string,
    docId: number | null
  ) => {
    console.log("Doc id = ", docId);
    setSelectedDocType(docType);
    setSelectedDocName(docName);
    setDocId(docId);
    setUploadDialogOpen(true);
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      setSelectedFile(e.target.files[0]);
    }
  };

  const handleUpload = async () => {
    if (!selectedFile) {
      toast.error("Please select a file to upload");
      return;
    }

    // Check file type (PDF only)
    if (selectedFile.type !== "application/pdf") {
      toast.error("Please upload only PDF files");
      return;
    }

    // Check file size (max 5MB)
    if (selectedFile.size > 5 * 1024 * 1024) {
      toast.error("File size should not exceed 5MB");
      return;
    }

    setIsUploading(true);
    try {
      const response = await uploadDocument(
        selectedFile,
        selectedDocType,
        docId
      );

      console.log("REsopnse = " + JSON.stringify(response));

      if (response.status === "SUCCESS") {
        const updatedDoc = response.data;

        setUploadDialogOpen(false);
        setSelectedFile(null);

        // Show success notification
        toast.success("Document uploaded successfully", {
          description: "Your document is now pending verification by our team",
          duration: 5000,
        });
      } else {
        toast.error(response.message || "Failed to upload document");
      }
    } catch (error) {
      console.error("Error uploading document:", error);
      toast.error("An error occurred while uploading the document");
    } finally {
      setIsUploading(false);
    }
  };

  // State for tracking which card is being edited
  const [editingCard, setEditingCard] = useState<string | null>(null);
  const [isSaving, setIsSaving] = useState(false);

  // Handle edit button click for a specific card
  const handleEditCard = (cardName: string) => {
    setEditingCard(cardName);
  };

  // Handle cancel button click
  const handleCancel = () => {
    setEditingCard(null);
  };

  // Handle save for documents
  const handleSaveDocuments = async () => {
    setIsSaving(true);
    try {
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1000));

      // Here you would typically update the data in your backend
      toast.success("Documents updated successfully");
      setEditingCard(null);
    } catch (error) {
      toast.error("Failed to update documents");
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <>
      <Card className="rounded-xl shadow-md bg-white border border-gray-100">
        <div className="h-1.5 bg-gradient-to-r from-[#00998F] via-teal-500 to-emerald-400"></div>
        <div className="">
          {isLoading ? (
            <div className="flex justify-center items-center">
              <svg
                className="animate-spin h-8 w-8 text-teal-500"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
              >
                <circle
                  className="opacity-25"
                  cx="12"
                  cy="12"
                  r="10"
                  stroke="currentColor"
                  strokeWidth="4"
                ></circle>
                <path
                  className="opacity-75"
                  fill="currentColor"
                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                ></path>
              </svg>
              <span className="ml-2 text-gray-600">Loading documents...</span>
            </div>
          ) : (
            <div className="px-4">
              {groupedDocuments.map((group) => (
                <div key={group.title} className="space-y-4">
                  <h3 className="text-lg py-4 font-medium mb-2">{group.title}</h3>
                  {group.keys.map((key) => {
                    const docType = documentTypes.find((d) => d.code === key);
                    if (!docType) return null;

                    const document =
                      documents.find((doc) => doc.attachmentType === docType.code) || {
                        id: -1,
                        objectKey: "",
                        attachmentTypeName: docType.name,
                        attachmentType: docType.code,
                        originalFilename: "",
                        remarks: null,
                        status: "NOT_UPLOADED",
                        uploadedAt: "",
                        uploadedBy: -1,
                        uploadedByEmail: "",
                      };

                    return (
                      <DocumentCard
                        key={docType.code}
                        document={document}
                        onUpload={() =>
                          handleUploadClick(
                            docType.code,
                            docType.name,
                            document?.id ?? null
                          )
                        }
                        description={
                          docType.description ||
                          `Upload your organization's ${docType.name}`
                        }
                      />
                    );
                  })}
                </div>
              ))}
            </div>
          )}
        </div>
        {/* Document Upload Dialog */}
        <Dialog open={uploadDialogOpen} onOpenChange={setUploadDialogOpen}>
          <DialogContent className="sm:max-w-md">
            <DialogHeader>
              <DialogTitle>Document Upload</DialogTitle>
              <DialogDescription>{selectedDocName}</DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="border-2 border-dashed border-gray-300 rounded-md p-6 text-center">
                <div className="flex flex-col items-center justify-center space-y-2">
                  <Upload className="text-teal-500 w-40 h-32" />
                  <p className="text-sm font-medium">Upload or Drop your file</p>
                  <p className="text-xs text-gray-500">Supported Formats: PDF</p>
                  <p className="text-xs text-gray-500">Maximum size: 5MB</p>
                  <Input
                    type="file"
                    accept="application/pdf"
                    onChange={handleFileChange}
                    className="mt-2"
                  />
                  {selectedFile && (
                    <p className="text-sm text-teal-600 mt-2">
                      Selected: {selectedFile.name} (
                      {(selectedFile.size / 1024 / 1024).toFixed(2)} MB)
                    </p>
                  )}
                </div>
              </div>
            </div>
            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => {
                  setUploadDialogOpen(false);
                  setSelectedFile(null);
                }}
              >
                Cancel
              </Button>
              <Button
                type="button"
                onClick={handleUpload}
                disabled={isUploading || !selectedFile}
                className="bg-teal-500 hover:bg-teal-600 text-white"
              >
                {isUploading ? (
                  <>
                    <svg
                      className="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                    >
                      <circle
                        className="opacity-25"
                        cx="12"
                        cy="12"
                        r="10"
                        stroke="currentColor"
                        strokeWidth="4"
                      ></circle>
                      <path
                        className="opacity-75"
                        fill="currentColor"
                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                      ></path>
                    </svg>
                    Uploading...
                  </>
                ) : (
                  "Continue"
                )}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </Card>
    </>
  );
}
