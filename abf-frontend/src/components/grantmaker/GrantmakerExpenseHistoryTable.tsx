'use client';

import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { motion } from "framer-motion";
import { FileText } from "lucide-react";

interface ExpenseHistoryRow {
  id: number;
  sr_no: number;
  particulars: string;
  main_header: string;
  sub_headers: string;
  units?: string;
  frequency?: string;
  cost_per_unit?: number;
  budget_quarterly: { Q1: number; Q2: number; Q3: number; Q4: number };
  actuals_quarterly: { Q1: number; Q2: number; Q3: number; Q4: number };
  total_budget?: number;
  total_actual?: number;
  receipt?: string;
  remarks?: string;
  source_type?: string;
  expense_date?: string;
  status?: string;
}

interface GrantmakerExpenseHistoryTableProps {
  expenses: ExpenseHistoryRow[];
  isLoading?: boolean;
}

export function GrantmakerExpenseHistoryTable({ 
  expenses, 
  isLoading = false 
}: GrantmakerExpenseHistoryTableProps) {
  
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-IN', { 
      style: 'currency', 
      currency: 'INR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(value);
  };



  const getStatusBadge = (status?: string) => {
    const statusConfig = {
      'approved': { color: 'bg-green-100 text-green-800', label: 'Approved' },
      'pending': { color: 'bg-yellow-100 text-yellow-800', label: 'Pending' },
      'rejected': { color: 'bg-red-100 text-red-800', label: 'Rejected' },
      'submitted': { color: 'bg-blue-100 text-blue-800', label: 'Submitted' },
      'draft': { color: 'bg-gray-100 text-gray-800', label: 'Draft' }
    };
    
    const config = statusConfig[status?.toLowerCase() as keyof typeof statusConfig] || 
                   statusConfig['submitted'];
    
    return (
      <Badge className={`${config.color} text-xs font-medium px-2 py-1`}>
        {config.label}
      </Badge>
    );
  };

  if (isLoading) {
    return (
      <Card className="shadow-sm border border-gray-100">
        <CardContent className="p-6">
          <div className="flex items-center justify-center py-12">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-teal-600 mx-auto mb-4"></div>
              <p className="text-gray-500">Loading expense history...</p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Mock data for demonstration when no real data is available
  const mockExpenses = [
    {
      id: 1,
      sr_no: 1,
      particulars: "Staff Salaries",
      main_header: "Personnel",
      sub_headers: "Permanent Staff",
      units: "Months",
      frequency: "12",
      cost_per_unit: 50000,
      budget_quarterly: { Q1: 150000, Q2: 150000, Q3: 150000, Q4: 150000 },
      actuals_quarterly: { Q1: 145000, Q2: 150000, Q3: 145000, Q4: 140000 },
      total_budget: 600000,
      total_actual: 580000,
      receipt: "Manual entry",
      remarks: "Some positions were vacant during the year",
      source_type: "manual",
      expense_date: "2024-01-15",
      status: "approved",
    },
    {
      id: 2,
      sr_no: 2,
      particulars: "Office Rent",
      main_header: "Operations",
      sub_headers: "Facilities",
      units: "Months",
      frequency: "12",
      cost_per_unit: 25000,
      budget_quarterly: { Q1: 75000, Q2: 75000, Q3: 75000, Q4: 75000 },
      actuals_quarterly: { Q1: 75000, Q2: 75000, Q3: 75000, Q4: 75000 },
      total_budget: 300000,
      total_actual: 300000,
      receipt: "rent_receipts_2024.xlsx",
      remarks: "",
      source_type: "excel",
      expense_date: "2024-02-01",
      status: "pending",
    },
    {
      id: 3,
      sr_no: 3,
      particulars: "Program Materials",
      main_header: "Programs",
      sub_headers: "Educational Materials",
      units: "Items",
      frequency: "4",
      cost_per_unit: 15000,
      budget_quarterly: { Q1: 15000, Q2: 15000, Q3: 15000, Q4: 15000 },
      actuals_quarterly: { Q1: 14500, Q2: 14500, Q3: 14500, Q4: 14500 },
      total_budget: 60000,
      total_actual: 58000,
      receipt: "program_materials.xlsx",
      remarks: "",
      source_type: "excel",
      expense_date: "2024-03-01",
      status: "approved",
    },
    {
      id: 4,
      sr_no: 4,
      particulars: "Travel Expenses",
      main_header: "Travel",
      sub_headers: "Field Visits",
      units: "Trips",
      frequency: "12",
      cost_per_unit: 2500,
      budget_quarterly: { Q1: 7500, Q2: 7500, Q3: 7500, Q4: 7500 },
      actuals_quarterly: { Q1: 8000, Q2: 8000, Q3: 8000, Q4: 8000 },
      total_budget: 30000,
      total_actual: 32000,
      receipt: "Manual entry",
      remarks: "Increased costs due to fuel price increases",
      source_type: "manual",
      expense_date: "2024-03-15",
      status: "approved",
    },
    {
      id: 5,
      sr_no: 5,
      particulars: "IT Equipment",
      main_header: "Equipment",
      sub_headers: "Hardware",
      units: "Items",
      frequency: "20",
      cost_per_unit: 2000,
      budget_quarterly: { Q1: 10000, Q2: 10000, Q3: 10000, Q4: 10000 },
      actuals_quarterly: { Q1: 9500, Q2: 9500, Q3: 9500, Q4: 9500 },
      total_budget: 40000,
      total_actual: 38000,
      receipt: "it_equipment.xlsx",
      remarks: "",
      source_type: "excel",
      expense_date: "2024-04-01",
      status: "pending",
    },
    {
      id: 6,
      sr_no: 6,
      particulars: "Utilities",
      main_header: "Operations",
      sub_headers: "Electricity & Water",
      units: "Months",
      frequency: "12",
      cost_per_unit: 8000,
      budget_quarterly: { Q1: 24000, Q2: 24000, Q3: 24000, Q4: 24000 },
      actuals_quarterly: { Q1: 25000, Q2: 24500, Q3: 24000, Q4: 23500 },
      total_budget: 96000,
      total_actual: 97000,
      receipt: "Manual entry",
      remarks: "Higher consumption in Q1 due to summer",
      source_type: "manual",
      expense_date: "2024-04-15",
      status: "approved",
    },
  ];

  const displayExpenses = expenses && expenses.length > 0 ? expenses : mockExpenses;

  if (!displayExpenses || displayExpenses.length === 0) {
    return (
      <Card className="shadow-sm border border-gray-100">
        <CardContent className="p-6">
          <div className="flex flex-col items-center justify-center py-12">
            <div className="w-16 h-16 bg-gradient-to-br from-teal-100 to-emerald-100 rounded-full flex items-center justify-center mb-4">
              <FileText className="h-8 w-8 text-teal-600" />
            </div>
            <div className="text-center">
              <p className="text-lg font-semibold text-gray-900 mb-1">No expense records found</p>
              <p className="text-sm text-gray-500">This grantee hasn't submitted any expenses yet</p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="mt-7">
      <Card className="shadow-sm border border-gray-100">
      <div className="px-6 py-4 border-b border-gray-200">
        <h3 className="text-lg font-semibold text-gray-800"> Expense History</h3>
        <p className="text-sm text-gray-600 mt-1">Complete breakdown of all expense submissions with quarterly details</p>
      </div>
      <CardContent className="p-0">
        <div className="overflow-x-auto">
          <div className="rounded-lg border-0 shadow-sm overflow-hidden">
            <div className="max-h-[600px] overflow-y-auto">
              <table className="w-full border-collapse bg-white min-w-[1400px]">
              <thead className="sticky top-0 z-10">
                <tr className="bg-gray-50 border-b border-gray-200">
                  <th className="px-6 py-4 text-center text-xs font-bold text-gray-900 uppercase tracking-wider">Sr.</th>
                  <th className="px-6 py-4 text-left text-xs font-bold text-gray-900 uppercase tracking-wider">Date</th>
                  <th className="px-6 py-4 text-left text-xs font-bold text-gray-900 uppercase tracking-wider">Particulars</th>
                  <th className="px-6 py-4 text-left text-xs font-bold text-gray-900 uppercase tracking-wider">Main header</th>
                  <th className="px-6 py-4 text-left text-xs font-bold text-gray-900 uppercase tracking-wider">Sub-headers</th>
                  <th className="px-6 py-4 text-left text-xs font-bold text-gray-900 uppercase tracking-wider">Units</th>
                  <th className="px-6 py-4 text-left text-xs font-bold text-gray-900 uppercase tracking-wider">Frequency</th>
                  <th className="px-6 py-4 text-right text-xs font-bold text-gray-900 uppercase tracking-wider">Cost per unit</th>
                  <th className="px-6 py-4 text-center text-xs font-bold text-gray-900 uppercase tracking-wider" colSpan={4}>Budget quarterly breakup</th>
                  <th className="px-6 py-4 text-center text-xs font-bold text-gray-900 uppercase tracking-wider">Total Budget</th>
                  <th className="px-6 py-4 text-center text-xs font-bold text-gray-900 uppercase tracking-wider" colSpan={4}>Actuals quarterly breakup</th>
                  <th className="px-6 py-4 text-center text-xs font-bold text-gray-900 uppercase tracking-wider">Total Actual</th>
                  <th className="px-6 py-4 text-center text-xs font-bold text-gray-900 uppercase tracking-wider">Status</th>
                </tr>
                <tr className="bg-gray-100 border-b border-gray-200 sticky top-16 z-10">
                  <th className="px-6 py-2" colSpan={8}></th>
                  <th className="px-6 py-2 text-center text-xs font-bold text-gray-900">Q1</th>
                  <th className="px-6 py-2 text-center text-xs font-bold text-gray-900">Q2</th>
                  <th className="px-6 py-2 text-center text-xs font-bold text-gray-900">Q3</th>
                  <th className="px-6 py-2 text-center text-xs font-bold text-gray-900">Q4</th>
                  <th className="px-6 py-2"></th>
                  <th className="px-6 py-2 text-center text-xs font-bold text-gray-900">Q1</th>
                  <th className="px-6 py-2 text-center text-xs font-bold text-gray-900">Q2</th>
                  <th className="px-6 py-2 text-center text-xs font-bold text-gray-900">Q3</th>
                  <th className="px-6 py-2 text-center text-xs font-bold text-gray-900">Q4</th>
                  <th className="px-6 py-2"></th>
                  <th className="px-6 py-2"></th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {displayExpenses.map((row, index) => (
                  <motion.tr
                    key={row.id}
                    className="hover:bg-gray-50"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ delay: index * 0.05 }}
                  >
                    <td className="px-6 py-4 text-center">
                      <span className="text-sm font-medium text-gray-900">
                        {row.sr_no || index + 1}
                      </span>
                    </td>
                    <td className="p-3 border-r border-gray-200/40">
                      <div className="text-sm font-medium text-gray-900">
                        {row.expense_date || 'N/A'}
                      </div>
                    </td>
                    <td className="p-3 border-r border-gray-200/40">
                      <div className="text-sm text-gray-900 max-w-[200px] truncate" title={row.particulars}>
                        {row.particulars}
                      </div>
                    </td>
                    <td className="p-3 border-r border-gray-200/40">
                      <div className="text-sm text-gray-900 max-w-[150px] truncate" title={row.main_header}>
                        {row.main_header}
                      </div>
                    </td>
                    <td className="p-3 border-r border-gray-200/40">
                      <div className="text-sm text-gray-900 max-w-[150px] truncate" title={row.sub_headers}>
                        {row.sub_headers}
                      </div>
                    </td>
                    <td className="p-3 border-r border-gray-200/40 text-center">
                      <div className="text-sm text-gray-700">
                        {row.units || '-'}
                      </div>
                    </td>
                    <td className="p-3 border-r border-gray-200/40 text-center">
                      <div className="text-sm text-gray-700">
                        {row.frequency || '-'}
                      </div>
                    </td>
                    <td className="p-3 border-r border-gray-200/40 text-right">
                      <div className="text-sm font-medium text-gray-900">
                        {row.cost_per_unit ? formatCurrency(row.cost_per_unit) : '-'}
                      </div>
                    </td>
                    {["Q1", "Q2", "Q3", "Q4"].map((quarter) => (
                      <td key={`budget_${quarter}`} className="p-3 border-r border-gray-200/40 text-right">
                        <div className="bg-gradient-to-r from-teal-50 to-emerald-50 px-2 py-1 rounded border border-teal-200/50">
                          <div className="text-xs font-semibold text-teal-700">
                            {formatCurrency(row.budget_quarterly[quarter as keyof typeof row.budget_quarterly] || 0)}
                          </div>
                        </div>
                      </td>
                    ))}
                    <td className="p-3 border-r border-gray-200/40 text-right">
                      <div className="bg-gradient-to-r from-teal-100 to-emerald-100 px-2 py-1.5 rounded-lg border border-teal-300/50">
                        <div className="text-sm font-bold text-teal-800">
                          {formatCurrency(row.total_budget || Object.values(row.budget_quarterly).reduce((acc, val) => acc + val, 0))}
                        </div>
                      </div>
                    </td>
                    {["Q1", "Q2", "Q3", "Q4"].map((quarter) => (
                      <td key={`actual_${quarter}`} className="p-3 border-r border-gray-200/40 text-right">
                        <div className="bg-gradient-to-r from-emerald-50 to-teal-50 px-2 py-1 rounded border border-emerald-200/50">
                          <div className="text-xs font-semibold text-emerald-700">
                            {formatCurrency(row.actuals_quarterly[quarter as keyof typeof row.actuals_quarterly] || 0)}
                          </div>
                        </div>
                      </td>
                    ))}
                    <td className="p-3 border-r border-gray-200/40 text-right">
                      <div className="bg-gradient-to-r from-emerald-100 to-teal-100 px-2 py-1.5 rounded-lg border border-emerald-300/50">
                        <div className="text-sm font-bold text-emerald-800">
                          {formatCurrency(row.total_actual || Object.values(row.actuals_quarterly).reduce((acc, val) => acc + val, 0))}
                        </div>
                      </div>
                    </td>
                    <td className="p-3 text-center">
                      {getStatusBadge(row.status)}
                    </td>
                  </motion.tr>
                ))}
              </tbody>
            </table>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
    </div>
  );
}
