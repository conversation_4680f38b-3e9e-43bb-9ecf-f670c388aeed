{"name": "abf-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "ngrok": "ngrok http 3000", "build": "next build", "start": "next start -p 3000 -H 0.0.0.0", "lint": "next lint"}, "dependencies": {"@hookform/resolvers": "^5.0.1", "@radix-ui/react-aspect-ratio": "^1.1.7", "@radix-ui/react-avatar": "^1.1.4", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.3", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-tooltip": "^1.2.7", "@react-map/india": "^1.0.21", "@tanstack/react-query": "^5.79.0", "@tanstack/react-table": "^8.21.3", "axios": "^1.8.4", "chart.js": "^4.4.9", "class-variance-authority": "^0.7.1", "classnames": "^2.5.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "file-saver": "^2.0.5", "file-server": "^2.2.1", "framer-motion": "^12.6.3", "html2canvas": "^1.4.1", "jspdf": "^3.0.1", "jszip": "^3.10.1", "lucide-react": "^0.479.0", "next": "^15.3.1", "prettier": "^3.5.3", "react": "^19.0.0", "react-chartjs-2": "^5.3.0", "react-datepicker": "^8.2.1", "react-day-picker": "^9.7.0", "react-dom": "^19.0.0", "react-hook-form": "^7.56.4", "recharts": "^2.15.2", "sonner": "^2.0.1", "tailwind-merge": "^3.0.2", "tailwindcss-animate": "^1.0.7", "tanstack": "^1.0.0", "xlsx": "^0.18.5", "zod": "^3.25.32"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/lodash": "^4.17.17", "@types/node": "20.17.30", "@types/react": "19.1.0", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.2.1", "ngrok": "^5.0.0-beta.2", "tailwindcss": "^4", "typescript": "5.8.3"}, "optionalDependencies": {"@tailwindcss/oxide-linux-x64-gnu": "^4.0.1", "lightningcss-linux-x64-gnu": "^1.29.1"}}